// API配置文件
export const API_CONFIG = {
  // 基础URL，生产环境时需要修改这里的IP和端口
  // BASE_URL: 'http://*************:7300',
  BASE_URL: 'https://*************/pgrest',

  // API端点
  ENDPOINTS: {
    BZJ_TEMPLATE: '/bzj_template',
    BZJ_USER: '/bzj_user',
    BZJ_MENU: '/bzj_menu',
    BZJ_ROLE: '/bzj_role',
    BZJ_ROLE_MENU: '/bzj_role_menu',
    BZJ_USER_ROLE: '/bzj_user_role'
  }
}

// 获取完整的API URL
export const getApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`
}

// 构建查询参数
export const buildQueryParams = (params: Record<string, any>): string => {
  const queryParams = new URLSearchParams()

  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      queryParams.append(key, value.toString())
    }
  })

  return queryParams.toString()
}
