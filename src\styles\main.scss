// ======================================================
// 合并后的样式文件
// 包含：
// 1. Element Plus 主题变量 (原 element/index.scss)
// 2. Element Plus 暗色主题变量 (原 element/dark.scss)
// 3. 全局样式 (原 index.scss)
// ======================================================

// ======================================================
// 第一部分: Element Plus 主题变量 (原 element/index.scss)
// ======================================================

$--colors: (
  'primary': (
    'base': green,
  ),
  'success': (
    'base': #21ba45,
  ),
  'warning': (
    'base': #f2711c,
  ),
  'danger': (
    'base': #db2828,
  ),
  'error': (
    'base': #db2828,
  ),
  'info': (
    'base': #42b8dd,
  ),
);

// we can add this to custom namespace, default is 'el'
@forward 'element-plus/theme-chalk/src/mixins/config.scss' with (
  $namespace: 'ep'
);

// You should use them in scss, because we calculate it by sass.
// comment next lines to use default color
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  // do not use same name, it will override.
  $colors: $--colors,
  $button-padding-horizontal: ('default': 50px)
);

// if you want to import all
// @use "element-plus/theme-chalk/src/index.scss" as *;

// You can comment it to hide debug info.
// @debug $--colors;

// ======================================================
// 第二部分: 暗色主题支持
// ======================================================

// 注意：暗色主题的自定义 CSS 变量已移至 dark.scss 文件
// 这里只导入 Element Plus 的基本暗色主题支持

// ======================================================
// 第三部分: 全局样式 (原 index.scss)
// ======================================================

// import dark theme
@use 'element-plus/theme-chalk/src/dark/css-vars.scss' as *;

// :root {
//   --ep-color-primary: red;
// }

body {
  font-family: Inter, system-ui, Avenir, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
}

a {
  color: var(--ep-color-primary);
}

code {
  border-radius: 2px;
  padding: 2px 4px;
  background-color: var(--ep-color-primary-light-9);
  color: var(--ep-color-primary);
}

#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: rgb(13, 148, 136);
  opacity: 0.75;
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
}

