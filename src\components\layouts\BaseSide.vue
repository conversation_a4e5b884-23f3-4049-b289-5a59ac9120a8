<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

interface MenuItem {
  index: string;
  title: string;
  children?: MenuItem[];
}

// 所有菜单项
const allMenuItems = ref<MenuItem[]>([
  {
    index: '1',
    title: '特殊保证金申请管理',
    children: [
      { index: '/template-management', title: '特保模板管理' },
      { index: '/param-config', title: '特保申请参数配置' },
      { index: '/template-query', title: '模板化特保查询' },
      { index: '/custom-query', title: '个性化特保查询' },
      { index: '/special-rate-application', title: '特殊保证金率申请' }
    ]
  },
  {
    index: '2',
    title: '业务办理中心',
    children: [
      { index: '/process-initiation', title: '发起流程' },
      { index: '/flow-details-special-rate', title: '流程明细--特殊保证金率申请' },
      { index: '/image-archive-oa', title: '影像归档--OA特保流程' }
    ]
  },
  {
    index: '3',
    title: '系统设置',
    children: [
      { index: '/menu-management', title: '菜单管理' },
      { index: '/role-management', title: '角色管理' },
      { index: '/user-management', title: '用户管理' }
    ]
  }
]);

// 根据当前路由计算显示的菜单项
const menuItems = computed(() => {
  const currentPath = route.path;

  // 如果当前路径包含系统管理相关的路由，只显示系统设置菜单
  if (currentPath.includes('/menu-management') ||
      currentPath.includes('/role-management') ||
      currentPath.includes('/user-management')) {
    return allMenuItems.value.filter(item => item.index === '3');
  }

  // 如果当前路径包含业务办理相关的路由，显示特殊保证金申请管理和业务办理中心菜单
  if (currentPath.includes('/process-initiation') ||
      currentPath.includes('/flow-details-special-rate') ||
      currentPath.includes('/image-archive-oa')) {
    return allMenuItems.value.filter(item => item.index === '1' || item.index === '2');
  }

  // 默认显示特殊保证金申请管理和业务办理中心菜单
  return allMenuItems.value.filter(item => item.index === '1' || item.index === '2');
});

const handleSelect = (index: string) => {
  router.push(index);
};
</script>

<template>
  <div class="sidebar">
    <el-menu
      default-active="1"
      class="el-menu-vertical"
      :collapse="false"
      @select="handleSelect"
    >
      <template v-for="item in menuItems" :key="item.index">
        <el-sub-menu :index="item.index">
          <template #title>
            <span>{{ item.title }}</span>
          </template>
          <el-menu-item
            v-for="child in item.children"
            :key="child.index"
            :index="child.index"
          >
            {{ child.title }}
          </el-menu-item>
        </el-sub-menu>
      </template>
    </el-menu>
  </div>
</template>

<style scoped>
.sidebar {
  height: 100%;
  border-right: 1px solid var(--ep-border-color-light);
}

.el-menu-vertical {
  height: 100%;
}
</style>