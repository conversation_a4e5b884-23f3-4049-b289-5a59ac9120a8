<template>
  <div class="user-management">
    <h2>用户管理</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="组织名称">
              <el-select v-model="queryForm.organizationName" placeholder="请选择组织" clearable style="width: 100%;">
                <el-option label="长江期货" value="长江期货" />
                <el-option label="长江证券" value="长江证券" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属部门">
              <el-tree-select
                v-model="queryForm.departments"
                :data="departmentTreeData"
                :props="{ label: 'name', value: 'code', children: 'children' }"
                placeholder="请选择部门"
                multiple
                filterable
                check-strictly
                clearable
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属角色">
              <el-select v-model="queryForm.roleId" placeholder="请选择角色" clearable style="width: 100%;">
                <el-option label="所有" value="" />
                <el-option label="系统管理员" value="1" />
                <el-option label="业务管理员" value="2" />
                <el-option label="风控专员" value="3" />
                <el-option label="普通用户" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="OA账号">
              <el-input v-model="queryForm.oaAccount" placeholder="请输入OA账号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="OA名称">
              <el-input v-model="queryForm.oaName" placeholder="请输入OA名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据来源">
              <el-select v-model="queryForm.dataSource" placeholder="请选择数据来源" clearable style="width: 100%;">
                <el-option label="每晚批量同步" value="每晚批量同步" />
                <el-option label="临时人工授权" value="临时人工授权" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button type="success" @click="handleAdd">新增</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="chorg_name" label="组织名称" width="120" />
        <el-table-column prop="chdept_code" label="部门编码" width="120" />
        <el-table-column prop="chdept_name" label="所属部门" width="150" />
        <el-table-column prop="oa_code" label="OA账号" width="120" />
        <el-table-column prop="oa_name" label="OA名称" width="120" />
        <el-table-column prop="hr_code" label="员工编码" width="120" />
        <el-table-column prop="chpost_name" label="所属角色" width="120" />
        <el-table-column prop="mobile_no" label="手机号码" width="130" />
        <el-table-column prop="fdlogin_flg" label="访问权限" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.fdlogin_flg === '是' ? 'success' : 'danger'">
              {{ scope.row.fdlogin_flg }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="oa_src" label="数据来源" width="130" />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="warning" size="small" @click="handleRole(scope.row)">角色</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          :pager-count="7"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="组织名称" prop="chorg_name">
              <el-select v-model="formData.chorg_name" placeholder="请选择组织" style="width: 100%;" @change="handleOrgChange">
                <el-option label="长江期货" value="长江期货" />
                <el-option label="长江证券" value="长江证券" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门编码" prop="chdept_code">
              <el-input v-model="formData.chdept_code" placeholder="请输入部门编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属部门" prop="chdept_name">
              <el-tree-select
                v-model="formData.chdept_name"
                :data="departmentTreeData"
                :props="{ label: 'name', value: 'name', children: 'children' }"
                placeholder="请选择部门"
                filterable
                check-strictly
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="OA账号" prop="oa_code">
              <el-input
                v-model="formData.oa_code"
                placeholder="请输入OA账号"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="OA名称" prop="oa_name">
              <el-input v-model="formData.oa_name" placeholder="请输入OA名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="员工编码" prop="hr_code">
              <el-input v-model="formData.hr_code" placeholder="请输入员工编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号码" prop="mobile_no">
              <el-input v-model="formData.mobile_no" placeholder="请输入手机号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="访问权限" prop="fdlogin_flg">
              <el-radio-group v-model="formData.fdlogin_flg">
                <el-radio value="是">是</el-radio>
                <el-radio value="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="数据来源" prop="oa_src">
              <el-radio-group v-model="formData.oa_src">
                <el-radio value="每晚批量同步">每晚批量同步</el-radio>
                <el-radio value="临时人工授权">临时人工授权</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit" :disabled="!formData.oa_code">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 绑定用户角色对话框 -->
    <el-dialog
      v-model="roleDialogVisible"
      title="绑定用户角色"
      width="600px"
    >
      <el-table :data="roleTableData" border style="width: 100%">
        <el-table-column width="50" align="center">
          <template #default="scope">
            <el-checkbox v-model="scope.row.checked" />
          </template>
        </el-table-column>
        <el-table-column prop="role_code" label="角色编号" width="120" />
        <el-table-column prop="role_name" label="角色名称" width="150" />
        <el-table-column prop="role_desc" label="角色说明" />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="roleDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleRoleSubmit">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import axios from 'axios'
import { getApiUrl, API_CONFIG, buildQueryParams } from '../config/api'

// 定义用户数据类型
interface BzjUser {
  chorg_code: string
  chorg_name: string
  chdept_code: string | null
  chdept_name: string | null
  hr_code: string | null
  oa_code: string
  oa_name: string
  mobile_no: string | null
  fdlogin_flg: string
  chpost_code: string | null
  chpost_name: string | null
  oa_src: string | null
}

// 定义角色数据类型
interface BzjRole {
  role_code: string
  role_name: string
  role_desc: string | null
}

// 定义用户角色关联类型
interface BzjUserRole {
  oa_code: string
  role_code: string
}

// 查询表单
const queryForm = reactive({
  organizationName: '',
  departments: [] as string[],
  roleId: '',
  oaAccount: '',
  oaName: '',
  dataSource: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 部门树数据
const departmentTreeData = ref([
  {
    code: 'DEPT001',
    name: '总经理办公室',
    children: [
      { code: 'DEPT001001', name: '董事会秘书处' },
      { code: 'DEPT001002', name: '法务合规部' }
    ]
  },
  {
    code: 'DEPT002',
    name: '风险管理部',
    children: [
      { code: 'DEPT002001', name: '市场风险管理部' },
      { code: 'DEPT002002', name: '信用风险管理部' },
      { code: 'DEPT002003', name: '操作风险管理部' }
    ]
  },
  {
    code: 'DEPT003',
    name: '业务部门',
    children: [
      { code: 'DEPT003001', name: '经纪业务部' },
      { code: 'DEPT003002', name: '资产管理部' },
      { code: 'DEPT003003', name: '投资咨询部' }
    ]
  },
  {
    code: 'DEPT004',
    name: '技术部门',
    children: [
      { code: 'DEPT004001', name: '信息技术部' },
      { code: 'DEPT004002', name: '系统运维部' }
    ]
  }
])

// 表格数据
const tableData = ref<BzjUser[]>([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isEdit = ref(false)

// 角色对话框
const roleDialogVisible = ref(false)

// 表单数据
const formData = reactive({
  chorg_code: '',
  chorg_name: '',
  chdept_code: '',
  chdept_name: '',
  hr_code: '',
  oa_code: '',
  oa_name: '',
  mobile_no: '',
  fdlogin_flg: '是',
  chpost_code: '',
  chpost_name: '',
  oa_src: '每晚批量同步'
})

// 角色表格数据
const roleTableData = ref<(BzjRole & { checked: boolean })[]>([])

// 当前操作的用户OA账号
const currentOaCode = ref('')

// 表单验证规则
const formRules = {
  chorg_name: [{ required: true, message: '请选择组织名称', trigger: 'change' }],
  chdept_code: [{ required: true, message: '请输入部门编码', trigger: 'blur' }],
  oa_code: [{ required: true, message: '请输入OA账号', trigger: 'blur' }],
  oa_name: [{ required: true, message: '请输入OA名称', trigger: 'blur' }],
  mobile_no: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ]
}

// 构建查询参数
const buildApiQueryParams = () => {
  const params: Record<string, string> = {}

  // 模糊查询
  if (queryForm.oaAccount) {
    params['oa_code'] = `like.*${queryForm.oaAccount}*`
  }
  if (queryForm.oaName) {
    params['oa_name'] = `like.*${queryForm.oaName}*`
  }
  if (queryForm.organizationName) {
    params['chorg_name'] = `eq.${queryForm.organizationName}`
  }
  if (queryForm.dataSource) {
    params['oa_src'] = `eq.${queryForm.dataSource}`
  }
  // 部门多选查询
  if (queryForm.departments.length > 0) {
    params['chdept_name'] = `in.(${queryForm.departments.join(',')})`
  }

  // 排序：默认按照oa_code升序
  params['order'] = 'oa_code.asc'

  // 分页
  params['limit'] = pageSize.value.toString()
  params['offset'] = ((currentPage.value - 1) * pageSize.value).toString()

  return buildQueryParams(params)
}

// 查询
const handleQuery = () => {
  currentPage.value = 1 // 重置到第一页
  fetchData()
}

// 重置
const resetQuery = () => {
  Object.assign(queryForm, {
    organizationName: '',
    departments: [],
    roleId: '',
    oaAccount: '',
    oaName: '',
    dataSource: ''
  })
  currentPage.value = 1
  fetchData()
}

// 分页大小改变
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchData()
}

// 当前页改变
const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  fetchData()
}

// 导出
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 加载数据
const fetchData = async () => {
  loading.value = true
  try {
    const queryString = buildApiQueryParams()
    const url = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_USER)}?${queryString}`

    const response = await axios.get(url)
    tableData.value = response.data || []

    // 获取总数
    const countParams: Record<string, string> = {}
    if (queryForm.oaAccount) {
      countParams['oa_code'] = `like.*${queryForm.oaAccount}*`
    }
    if (queryForm.oaName) {
      countParams['oa_name'] = `like.*${queryForm.oaName}*`
    }
    if (queryForm.organizationName) {
      countParams['chorg_name'] = `eq.${queryForm.organizationName}`
    }
    if (queryForm.dataSource) {
      countParams['oa_src'] = `eq.${queryForm.dataSource}`
    }
    if (queryForm.departments.length > 0) {
      countParams['chdept_name'] = `in.(${queryForm.departments.join(',')})`
    }
    countParams['select'] = 'count'

    const countQueryString = buildQueryParams(countParams)
    const countUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_USER)}?${countQueryString}`
    const countResponse = await axios.get(countUrl, {
      headers: {
        'Prefer': 'count=exact'
      }
    })

    // 根据PostgREST的响应格式获取总数
    totalCount.value = parseInt(countResponse.headers['content-range']?.split('/')[1] || '0') ||
                      countResponse.data[0]?.count || 0

  } catch (error) {
    console.error('Error fetching data:', error)
    ElMessage.error('数据加载失败，请稍后重试')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增用户'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row: BzjUser) => {
  dialogTitle.value = '修改用户'
  isEdit.value = true
  Object.assign(formData, {
    chorg_code: row.chorg_code,
    chorg_name: row.chorg_name,
    chdept_code: row.chdept_code || '',
    chdept_name: row.chdept_name || '',
    hr_code: row.hr_code || '',
    oa_code: row.oa_code,
    oa_name: row.oa_name,
    mobile_no: row.mobile_no || '',
    fdlogin_flg: row.fdlogin_flg,
    chpost_code: row.chpost_code || '',
    chpost_name: row.chpost_name || '',
    oa_src: row.oa_src || '每晚批量同步'
  })
  dialogVisible.value = true
}

// 角色管理
const handleRole = async (row: BzjUser) => {
  currentOaCode.value = row.oa_code

  // 加载角色数据（如果还没有加载）
  if (roleTableData.value.length === 0) {
    await fetchRoleData()
  }

  // 获取用户已有的角色
  await fetchUserRoles(row.oa_code)

  roleDialogVisible.value = true
}

// 删除
const handleDelete = async (row: BzjUser) => {
  try {
    await ElMessageBox.confirm(`确认要删除用户[${row.oa_name}]？`, '提示', {
      confirmButtonText: '是',
      cancelButtonText: '否',
      type: 'warning'
    })

    // 调用API删除数据
    const deleteUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_USER)}?oa_code=eq.${row.oa_code}`
    await axios.delete(deleteUrl)

    ElMessage.success('删除成功')

    // 重新加载数据
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting data:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}



// 加载角色数据
const fetchRoleData = async () => {
  try {
    // 获取所有角色数据
    const url = `https://172.16.131.95/pgrest${API_CONFIG.ENDPOINTS.BZJ_ROLE}?order=role_code.asc`
    const response = await axios.get(url)
    const roles = response.data || []

    // 转换为带有checked属性的格式
    roleTableData.value = roles.map((role: BzjRole) => ({
      ...role,
      checked: false
    }))

  } catch (error) {
    console.error('Error fetching role data:', error)
    ElMessage.error('角色数据加载失败')
  }
}

// 获取用户角色权限
const fetchUserRoles = async (oaCode: string) => {
  try {
    const url = `https://172.16.131.95/pgrest${API_CONFIG.ENDPOINTS.BZJ_USER_ROLE}?oa_code=eq.${oaCode}`
    const response = await axios.get(url)
    const userRoles = response.data || []

    // 提取角色编码列表
    const userRoleCodes = userRoles.map((ur: BzjUserRole) => ur.role_code)

    // 设置角色选中状态
    roleTableData.value.forEach(role => {
      role.checked = userRoleCodes.includes(role.role_code)
    })

  } catch (error) {
    console.error('Error fetching user roles:', error)
    ElMessage.error('获取用户角色失败')
    // 重置所有角色为未选中状态
    roleTableData.value.forEach(role => {
      role.checked = false
    })
  }
}

// 组织变更处理
const handleOrgChange = (value: string) => {
  // 根据组织名称设置组织代码
  if (value === '长江期货') {
    formData.chorg_code = 'CJQH'
  } else if (value === '长江证券') {
    formData.chorg_code = 'CJZQ'
  } else {
    formData.chorg_code = ''
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formData.oa_code) {
    ElMessage.warning('请输入OA账号')
    return
  }

  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构建请求数据
    const requestData = {
      chorg_code: formData.chorg_code || '',
      chorg_name: formData.chorg_name,
      chdept_code: formData.chdept_code || null,
      chdept_name: formData.chdept_name || null,
      hr_code: formData.hr_code || null,
      oa_code: formData.oa_code,
      oa_name: formData.oa_name,
      mobile_no: formData.mobile_no || null,
      fdlogin_flg: formData.fdlogin_flg,
      chpost_code: formData.chpost_code || null,
      chpost_name: formData.chpost_name || null,
      oa_src: formData.oa_src
    }

    if (isEdit.value) {
      // 修改
      const updateUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_USER)}?oa_code=eq.${formData.oa_code}`
      await axios.patch(updateUrl, requestData)
      ElMessage.success('修改成功')
    } else {
      // 新增
      await axios.post(getApiUrl(API_CONFIG.ENDPOINTS.BZJ_USER), requestData)
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
    resetForm()
    // 重新加载数据
    fetchData()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 提交角色
const handleRoleSubmit = async () => {
  try {
    const selectedRoles = roleTableData.value.filter(role => role.checked)

    // 先删除该用户的所有角色
    const deleteUrl = `https://172.16.131.95/pgrest${API_CONFIG.ENDPOINTS.BZJ_USER_ROLE}?oa_code=eq.${currentOaCode.value}`
    await axios.delete(deleteUrl)

    // 批量插入新的用户角色
    if (selectedRoles.length > 0) {
      const userRoles = selectedRoles.map(role => ({
        oa_code: currentOaCode.value,
        role_code: role.role_code
      }))

      await axios.post(`https://172.16.131.95/pgrest${API_CONFIG.ENDPOINTS.BZJ_USER_ROLE}`, userRoles)
    }

    ElMessage.success('角色配置成功')
    roleDialogVisible.value = false
  } catch (error) {
    console.error('Error saving user roles:', error)
    ElMessage.error('角色配置失败，请稍后重试')
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    chorg_code: '',
    chorg_name: '',
    chdept_code: '',
    chdept_name: '',
    hr_code: '',
    oa_code: '',
    oa_name: '',
    mobile_no: '',
    fdlogin_flg: '是',
    chpost_code: '',
    chpost_name: '',
    oa_src: '每晚批量同步'
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 关闭对话框
const handleDialogClose = () => {
  resetForm()
}

onMounted(() => {
  // 初始化数据
  fetchData()
  fetchRoleData()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
