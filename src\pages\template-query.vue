<template>
  <div class="template-query">
    <h2>模板化特保查询</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="投资者代码">
              <el-input
                v-model="queryForm.investorCode"
                placeholder="支持批量输入，逗号隔开"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保证金率模板代码">
              <el-input
                v-model="queryForm.templateCode"
                placeholder="支持模糊匹配"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="投资者属性">
              <el-select
                v-model="queryForm.investorProperty"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="007-1021-QH江汉营业部" value="007-1021-QH江汉营业部" />
                <el-option label="007-1022-QH武汉营业部" value="007-1022-QH武汉营业部" />
                <el-option label="007-1023-QH上海营业部" value="007-1023-QH上海营业部" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="模板类型">
              <el-select
                v-model="queryForm.templateType"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="加法" value="加法" />
                <el-option label="乘法" value="乘法" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="上浮点数">
              <el-select
                v-model="queryForm.floatPoint"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="0" value="0" />
                <el-option label="0.01" value="0.01" />
                <el-option label="0.02" value="0.02" />
                <el-option label="0.03" value="0.03" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="投资者类型">
              <el-select
                v-model="queryForm.investorType"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="自然人" value="自然人" />
                <el-option label="一般法人" value="一般法人" />
                <el-option label="特殊法人" value="特殊法人" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="组织构架">
              <el-autocomplete
                v-model="queryForm.organization"
                :fetch-suggestions="queryOrganizations"
                placeholder="请输入"
                clearable
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属部门">
              <el-autocomplete
                v-model="queryForm.department"
                :fetch-suggestions="queryDepartments"
                placeholder="请输入"
                clearable
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="对应风控专员">
              <el-autocomplete
                v-model="queryForm.riskOfficer"
                :fetch-suggestions="queryRiskOfficers"
                placeholder="请输入"
                clearable
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否活跃">
              <el-select
                v-model="queryForm.isActive"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="休眠状态">
              <el-select
                v-model="queryForm.sleepStatus"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="活跃" value="活跃" />
                <el-option label="休眠" value="休眠" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-dropdown @command="handleExport" style="margin-left: 10px;">
                <el-button type="success">
                  导出数据
                  <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="current">导出当页</el-dropdown-item>
                    <el-dropdown-item command="all">导出所有</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
        border
        style="width: 100%"
        :max-height="600"
      >
      <el-table-column prop="investorCode" label="投资者代码" width="100" fixed="left" />
      <el-table-column prop="investorName" label="投资者名称" width="100" fixed="left" />
      <el-table-column prop="templateCode" label="保证金模板代码" width="150" />
      <el-table-column prop="templateName" label="保证金模板名称" min-width="200" />
      <el-table-column prop="templateRemark" label="模板备注" width="120" />
      <el-table-column prop="investorProperty" label="投资者属性" min-width="180" />
      <el-table-column prop="templateType" label="模板类型" width="100" />
      <el-table-column prop="floatPoint" label="上浮点数" width="100" />
      <el-table-column prop="investorType" label="投资者类型" width="120" />
      <el-table-column prop="salesType" label="直销/IB" width="100" />
      <el-table-column prop="organization" label="组织构架" min-width="180" />
      <el-table-column prop="department" label="所属部门" width="120" />
      <el-table-column prop="isActive" label="是否活跃" width="100" />
      <el-table-column prop="sleepStatus" label="休眠状态" width="100" />
      <el-table-column prop="investorPhone" label="投资者手机" width="130" />
      <el-table-column prop="riskOfficer" label="对应风控专员" width="120" />
      <el-table-column prop="riskOfficerPhone" label="对应风控专员手机" width="150" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length"
          :pager-count="7"
        />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'

// 查询表单
const queryForm = reactive({
  investorCode: '',
  templateCode: '',
  investorProperty: '',
  templateType: '',
  floatPoint: '',
  investorType: '',
  organization: '',
  department: '',
  riskOfficer: '',
  isActive: '',
  sleepStatus: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 自动完成相关
const queryOrganizations = (queryString: string, cb: any) => {
  const results = queryString
    ? organizations.filter(org => org.value.toLowerCase().includes(queryString.toLowerCase()))
    : organizations
  cb(results)
}

const queryDepartments = (queryString: string, cb: any) => {
  const results = queryString
    ? departments.filter(dept => dept.value.toLowerCase().includes(queryString.toLowerCase()))
    : departments
  cb(results)
}

const queryRiskOfficers = (queryString: string, cb: any) => {
  const results = queryString
    ? riskOfficers.filter(officer => officer.value.toLowerCase().includes(queryString.toLowerCase()))
    : riskOfficers
  cb(results)
}

// 自动完成选项数据
const organizations = [
  { value: '102108-QH江汉营业部市场四部' },
  { value: '102109-QH江汉营业部市场五部' },
  { value: '102110-QH武汉营业部市场一部' },
  { value: '102111-QH武汉营业部市场二部' },
  { value: '102112-QH上海营业部市场一部' }
]

const departments = [
  { value: '江汉营业部' },
  { value: '武汉营业部' },
  { value: '上海营业部' },
  { value: '北京营业部' },
  { value: '深圳营业部' }
]

const riskOfficers = [
  { value: '吕攀' },
  { value: '张三' },
  { value: '李四' },
  { value: '王五' },
  { value: '赵六' }
]

// 表格数据
const tableData = ref([
  {
    investorCode: '60022183',
    investorName: '明维',
    templateCode: '5A000A',
    templateName: '上海/大连/郑州/能源/中金+0%',
    templateRemark: 'wm测试',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '自然人',
    salesType: '直销',
    organization: '102108-QH江汉营业部市场四部',
    department: '江汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419556111',
    riskOfficer: '吕攀',
    riskOfficerPhone: '13512344567'
  }
])

// 查询方法
const handleQuery = () => {
  // 实际项目中这里应该调用API进行查询
  ElMessage.success('查询成功')
  // 模拟过滤
  const filteredData = mockData.filter(item => {
    const codeMatch = !queryForm.investorCode ||
      queryForm.investorCode.split(',').some(code => item.investorCode.includes(code.trim()))
    const templateCodeMatch = !queryForm.templateCode || item.templateCode.includes(queryForm.templateCode)
    const propertyMatch = !queryForm.investorProperty || item.investorProperty === queryForm.investorProperty
    const typeMatch = !queryForm.templateType || item.templateType === queryForm.templateType
    const floatMatch = !queryForm.floatPoint || item.floatPoint === queryForm.floatPoint
    const investorTypeMatch = !queryForm.investorType || item.investorType === queryForm.investorType
    const orgMatch = !queryForm.organization || item.organization.includes(queryForm.organization)
    const deptMatch = !queryForm.department || item.department.includes(queryForm.department)
    const officerMatch = !queryForm.riskOfficer || item.riskOfficer.includes(queryForm.riskOfficer)
    const activeMatch = !queryForm.isActive || item.isActive === queryForm.isActive
    const sleepMatch = !queryForm.sleepStatus || item.sleepStatus === queryForm.sleepStatus

    return codeMatch && templateCodeMatch && propertyMatch && typeMatch && floatMatch &&
      investorTypeMatch && orgMatch && deptMatch && officerMatch && activeMatch && sleepMatch
  })

  tableData.value = filteredData
  currentPage.value = 1
}

// 重置查询
const resetQuery = () => {
  Object.keys(queryForm).forEach(key => {
    (queryForm as {[key: string]: string})[key] = ''
  })
  // 重新加载数据
  loadData()
}

// 导出数据
const handleExport = (command: string) => {
  try {
    // 定义要导出的数据
    let dataToExport: any[] = []

    if (command === 'current') {
      // 导出当前页数据
      dataToExport = tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
      ElMessage.success('导出当页数据成功')
    } else if (command === 'all') {
      // 导出所有数据
      dataToExport = tableData.value
      ElMessage.success('导出所有数据成功')
    }

    // 定义表头
    const headers = [
      '投资者代码', '投资者名称', '保证金模板代码', '保证金模板名称', '模板备注',
      '投资者属性', '模板类型', '上浮点数', '投资者类型', '直销/IB',
      '组织构架', '所属部门', '是否活跃', '休眠状态', '投资者手机',
      '对应风控专员', '对应风控专员手机'
    ]

    // 转换数据格式
    const excelData = [headers]

    dataToExport.forEach(item => {
      const row = [
        item.investorCode,
        item.investorName,
        item.templateCode,
        item.templateName,
        item.templateRemark,
        item.investorProperty,
        item.templateType,
        item.floatPoint,
        item.investorType,
        item.salesType,
        item.organization,
        item.department,
        item.isActive,
        item.sleepStatus,
        item.investorPhone,
        item.riskOfficer,
        item.riskOfficerPhone
      ]
      excelData.push(row)
    })

    // 创建工作簿
    const worksheet = XLSX.utils.aoa_to_sheet(excelData)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, '模板化特保查询')

    // 设置列宽
    const columnWidths = [
      { wch: 10 }, // 投资者代码
      { wch: 10 }, // 投资者名称
      { wch: 15 }, // 保证金模板代码
      { wch: 25 }, // 保证金模板名称
      { wch: 12 }, // 模板备注
      { wch: 20 }, // 投资者属性
      { wch: 10 }, // 模板类型
      { wch: 10 }, // 上浮点数
      { wch: 12 }, // 投资者类型
      { wch: 10 }, // 直销/IB
      { wch: 25 }, // 组织构架
      { wch: 12 }, // 所属部门
      { wch: 10 }, // 是否活跃
      { wch: 10 }, // 休眠状态
      { wch: 15 }, // 投资者手机
      { wch: 12 }, // 对应风控专员
      { wch: 15 }  // 对应风控专员手机
    ]
    worksheet['!cols'] = columnWidths

    // 导出文件
    const fileName = command === 'current' ? '模板化特保查询_当前页.xlsx' : '模板化特保查询_全部数据.xlsx'
    XLSX.writeFile(workbook, fileName)
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 加载数据
const loadData = () => {
  // 实际项目中这里应该调用API获取数据
  // 这里使用静态数据
  tableData.value = mockData
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})

// 模拟数据
const mockData = [
  {
    investorCode: '60022183',
    investorName: '明维',
    templateCode: '5A000A',
    templateName: '上海/大连/郑州/能源/中金+0%',
    templateRemark: 'wm测试',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '自然人',
    salesType: '直销',
    organization: '102108-QH江汉营业部市场四部',
    department: '江汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419556111',
    riskOfficer: '吕攀',
    riskOfficerPhone: '13512344567'
  },
  {
    investorCode: '60022184',
    investorName: '张伟',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '自然人',
    salesType: '直销',
    organization: '102108-QH江汉营业部市场四部',
    department: '江汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419556222',
    riskOfficer: '吕攀',
    riskOfficerPhone: '13512344567'
  },
  {
    investorCode: '60022185',
    investorName: '王芳',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '自然人',
    salesType: '直销',
    organization: '102110-QH武汉营业部市场一部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419556333',
    riskOfficer: '张三',
    riskOfficerPhone: '13512344568'
  },
  {
    investorCode: '60022186',
    investorName: '李娜',
    templateCode: '5B000A',
    templateName: '广州/上海/大连/郑州/能源+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '自然人',
    salesType: 'IB',
    organization: '102111-QH武汉营业部市场二部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419556444',
    riskOfficer: '张三',
    riskOfficerPhone: '13512344568'
  },
  {
    investorCode: '60022187',
    investorName: '赵明',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '自然人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419556555',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022188',
    investorName: '钱江',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '一般法人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419556666',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022189',
    investorName: '孙立',
    templateCode: '5A000A',
    templateName: '上海/大连/郑州/能源/中金+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '一般法人',
    salesType: 'IB',
    organization: '102109-QH江汉营业部市场五部',
    department: '江汉营业部',
    isActive: '否',
    sleepStatus: '休眠',
    investorPhone: '13419556777',
    riskOfficer: '王五',
    riskOfficerPhone: '13512344570'
  },
  {
    investorCode: '60022190',
    investorName: '周红',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '一般法人',
    salesType: '直销',
    organization: '102109-QH江汉营业部市场五部',
    department: '江汉营业部',
    isActive: '否',
    sleepStatus: '休眠',
    investorPhone: '13419556888',
    riskOfficer: '王五',
    riskOfficerPhone: '13512344570'
  },
  {
    investorCode: '60022191',
    investorName: '吴刚',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '特殊法人',
    salesType: '直销',
    organization: '102110-QH武汉营业部市场一部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419556999',
    riskOfficer: '赵六',
    riskOfficerPhone: '13512344571'
  },
  {
    investorCode: '60022192',
    investorName: '郑强',
    templateCode: '5B000A',
    templateName: '广州/上海/大连/郑州/能源+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '特殊法人',
    salesType: 'IB',
    organization: '102111-QH武汉营业部市场二部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557000',
    riskOfficer: '赵六',
    riskOfficerPhone: '13512344571'
  },
  {
    investorCode: '60022193',
    investorName: '马超',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '特殊法人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557001',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022194',
    investorName: '黄磊',
    templateCode: '5A000A',
    templateName: '上海/大连/郑州/能源/中金+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '自然人',
    salesType: '直销',
    organization: '102108-QH江汉营业部市场四部',
    department: '江汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557002',
    riskOfficer: '吕攀',
    riskOfficerPhone: '13512344567'
  },
  {
    investorCode: '60022195',
    investorName: '杨光',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '自然人',
    salesType: '直销',
    organization: '102108-QH江汉营业部市场四部',
    department: '江汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557003',
    riskOfficer: '吕攀',
    riskOfficerPhone: '13512344567'
  },
  {
    investorCode: '60022196',
    investorName: '刘芳',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '自然人',
    salesType: '直销',
    organization: '102110-QH武汉营业部市场一部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557004',
    riskOfficer: '张三',
    riskOfficerPhone: '13512344568'
  },
  {
    investorCode: '60022197',
    investorName: '陈明',
    templateCode: '5B000A',
    templateName: '广州/上海/大连/郑州/能源+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '自然人',
    salesType: 'IB',
    organization: '102111-QH武汉营业部市场二部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557005',
    riskOfficer: '张三',
    riskOfficerPhone: '13512344568'
  },
  {
    investorCode: '60022198',
    investorName: '林强',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '自然人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557006',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022199',
    investorName: '张鑫',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '一般法人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557007',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022200',
    investorName: '王丽',
    templateCode: '5A000A',
    templateName: '上海/大连/郑州/能源/中金+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '一般法人',
    salesType: 'IB',
    organization: '102109-QH江汉营业部市场五部',
    department: '江汉营业部',
    isActive: '否',
    sleepStatus: '休眠',
    investorPhone: '13419557008',
    riskOfficer: '王五',
    riskOfficerPhone: '13512344570'
  },
  {
    investorCode: '60022201',
    investorName: '李明',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '一般法人',
    salesType: '直销',
    organization: '102109-QH江汉营业部市场五部',
    department: '江汉营业部',
    isActive: '否',
    sleepStatus: '休眠',
    investorPhone: '13419557009',
    riskOfficer: '王五',
    riskOfficerPhone: '13512344570'
  },
  {
    investorCode: '60022202',
    investorName: '赵刚',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '特殊法人',
    salesType: '直销',
    organization: '102110-QH武汉营业部市场一部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557010',
    riskOfficer: '赵六',
    riskOfficerPhone: '13512344571'
  },
  {
    investorCode: '60022203',
    investorName: '钱芳',
    templateCode: '5B000A',
    templateName: '广州/上海/大连/郑州/能源+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '特殊法人',
    salesType: 'IB',
    organization: '102111-QH武汉营业部市场二部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557011',
    riskOfficer: '赵六',
    riskOfficerPhone: '13512344571'
  },
  {
    investorCode: '60022204',
    investorName: '孙强',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '特殊法人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557012',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022205',
    investorName: '周明',
    templateCode: '5A000A',
    templateName: '上海/大连/郑州/能源/中金+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '自然人',
    salesType: '直销',
    organization: '102108-QH江汉营业部市场四部',
    department: '江汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557013',
    riskOfficer: '吕攀',
    riskOfficerPhone: '13512344567'
  },
  {
    investorCode: '60022206',
    investorName: '吴芳',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '自然人',
    salesType: '直销',
    organization: '102108-QH江汉营业部市场四部',
    department: '江汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557014',
    riskOfficer: '吕攀',
    riskOfficerPhone: '13512344567'
  },
  {
    investorCode: '60022207',
    investorName: '郑丽',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '自然人',
    salesType: '直销',
    organization: '102110-QH武汉营业部市场一部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557015',
    riskOfficer: '张三',
    riskOfficerPhone: '13512344568'
  },
  {
    investorCode: '60022208',
    investorName: '王强',
    templateCode: '5B000A',
    templateName: '广州/上海/大连/郑州/能源+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '自然人',
    salesType: 'IB',
    organization: '102111-QH武汉营业部市场二部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557016',
    riskOfficer: '张三',
    riskOfficerPhone: '13512344568'
  },
  {
    investorCode: '60022209',
    investorName: '李刚',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '自然人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557017',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022210',
    investorName: '张强',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '一般法人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557018',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022211',
    investorName: '赵丽',
    templateCode: '5A000A',
    templateName: '上海/大连/郑州/能源/中金+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '一般法人',
    salesType: 'IB',
    organization: '102109-QH江汉营业部市场五部',
    department: '江汉营业部',
    isActive: '否',
    sleepStatus: '休眠',
    investorPhone: '13419557019',
    riskOfficer: '王五',
    riskOfficerPhone: '13512344570'
  },
  {
    investorCode: '60022212',
    investorName: '钱明',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '一般法人',
    salesType: '直销',
    organization: '102109-QH江汉营业部市场五部',
    department: '江汉营业部',
    isActive: '否',
    sleepStatus: '休眠',
    investorPhone: '13419557020',
    riskOfficer: '王五',
    riskOfficerPhone: '13512344570'
  },
  {
    investorCode: '60022213',
    investorName: '孙丽',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '特殊法人',
    salesType: '直销',
    organization: '102110-QH武汉营业部市场一部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557021',
    riskOfficer: '赵六',
    riskOfficerPhone: '13512344571'
  },
  {
    investorCode: '60022214',
    investorName: '周刚',
    templateCode: '5B000A',
    templateName: '广州/上海/大连/郑州/能源+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '特殊法人',
    salesType: 'IB',
    organization: '102111-QH武汉营业部市场二部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557022',
    riskOfficer: '赵六',
    riskOfficerPhone: '13512344571'
  },
  {
    investorCode: '60022215',
    investorName: '吴丽',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '特殊法人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557023',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022216',
    investorName: '郑明',
    templateCode: '5A000A',
    templateName: '上海/大连/郑州/能源/中金+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '自然人',
    salesType: '直销',
    organization: '102108-QH江汉营业部市场四部',
    department: '江汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557024',
    riskOfficer: '吕攀',
    riskOfficerPhone: '13512344567'
  },
  {
    investorCode: '60022217',
    investorName: '马丽',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '自然人',
    salesType: '直销',
    organization: '102108-QH江汉营业部市场四部',
    department: '江汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557025',
    riskOfficer: '吕攀',
    riskOfficerPhone: '13512344567'
  },
  {
    investorCode: '60022218',
    investorName: '黄强',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '自然人',
    salesType: '直销',
    organization: '102110-QH武汉营业部市场一部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557026',
    riskOfficer: '张三',
    riskOfficerPhone: '13512344568'
  },
  {
    investorCode: '60022219',
    investorName: '杨丽',
    templateCode: '5B000A',
    templateName: '广州/上海/大连/郑州/能源+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1022-QH武汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '自然人',
    salesType: 'IB',
    organization: '102111-QH武汉营业部市场二部',
    department: '武汉营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557027',
    riskOfficer: '张三',
    riskOfficerPhone: '13512344568'
  },
  {
    investorCode: '60022220',
    investorName: '刘强',
    templateCode: '4A003A',
    templateName: '上海/大连/郑州/能源+3%',
    templateRemark: '高风险客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '加法',
    floatPoint: '0.03',
    investorType: '自然人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557028',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022221',
    investorName: '陈丽',
    templateCode: '1A100M',
    templateName: '郑州*1倍',
    templateRemark: '标准客户',
    investorProperty: '007-1023-QH上海营业部',
    templateType: '乘法',
    floatPoint: '1.00',
    investorType: '一般法人',
    salesType: '直销',
    organization: '102112-QH上海营业部市场一部',
    department: '上海营业部',
    isActive: '是',
    sleepStatus: '活跃',
    investorPhone: '13419557029',
    riskOfficer: '李四',
    riskOfficerPhone: '13512344569'
  },
  {
    investorCode: '60022222',
    investorName: '林丽',
    templateCode: '5A000A',
    templateName: '上海/大连/郑州/能源/中金+0%',
    templateRemark: '普通客户',
    investorProperty: '007-1021-QH江汉营业部',
    templateType: '加法',
    floatPoint: '0.00',
    investorType: '一般法人',
    salesType: 'IB',
    organization: '102109-QH江汉营业部市场五部',
    department: '江汉营业部',
    isActive: '否',
    sleepStatus: '休眠',
    investorPhone: '13419557030',
    riskOfficer: '王五',
    riskOfficerPhone: '13512344570'
  }
]
</script>

<style scoped>
.template-query {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}



.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>


