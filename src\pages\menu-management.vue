<template>
  <div class="menu-management">
    <h2>菜单管理</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="菜单名称">
              <el-input v-model="queryForm.menuName" placeholder="请输入菜单名称" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button type="success" @click="handleAdd">新增</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" row-key="menu_id" default-expand-all v-loading="loading">
        <el-table-column prop="menu_name" label="菜单名称" width="200" />
        <el-table-column prop="menu_desc" label="菜单说明" />
        <el-table-column prop="link_url" label="外链URL" width="200" />
        <el-table-column prop="menu_type" label="菜单类型" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.menu_type === 'M'" type="info">目录</el-tag>
            <el-tag v-else-if="scope.row.menu_type === 'C'" type="primary">菜单</el-tag>
            <el-tag v-else-if="scope.row.menu_type === 'F'" type="warning">按钮</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="success" size="small" @click="handleAddChild(scope.row)">添加</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/修改/添加菜单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item v-if="isAddChild" label="上级菜单编号" prop="parent_id">
          <el-input v-model="formData.parent_id" placeholder="上级菜单编号" :disabled="true" />
        </el-form-item>
        <el-form-item label="菜单名称" prop="menu_name">
          <el-input v-model="formData.menu_name" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="菜单类型" prop="menu_type">
          <el-select v-model="formData.menu_type" placeholder="请选择菜单类型" style="width: 100%;">
            <el-option label="目录" value="M" />
            <el-option label="菜单" value="C" />
            <el-option label="按钮" value="F" />
          </el-select>
        </el-form-item>
        <el-form-item label="链接类型" prop="is_link">
          <el-select v-model="formData.is_link" placeholder="请选择链接类型" style="width: 100%;">
            <el-option label="非外链" value="0" />
            <el-option label="外链" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="外链URL" prop="link_url">
          <el-input v-model="formData.link_url" placeholder="请输入外链URL" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" controls-position="right" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="菜单说明" prop="menu_desc">
          <el-input v-model="formData.menu_desc" placeholder="请输入菜单说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import axios from 'axios'
import { getApiUrl, API_CONFIG, buildQueryParams } from '../config/api'

// 定义菜单数据类型
interface BzjMenu {
  menu_id: string
  menu_name: string
  is_link: string
  link_url: string | null
  parent_id: string | null
  menu_type: string
  menu_desc: string | null
  sort: number | null
  children?: BzjMenu[]
}

// 查询表单
const queryForm = reactive({
  menuName: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 生成菜单ID的计数器
let menuIdCounter = 100

// 表格数据
const tableData = ref<BzjMenu[]>([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isAddChild = ref(false)

// 表单数据
const formData = reactive({
  menu_id: '',
  parent_id: null as string | null,
  menu_name: '',
  is_link: '0',
  link_url: '',
  menu_type: 'C',
  menu_desc: '',
  sort: 0
})

// 表单验证规则
const formRules = {
  menu_name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
  menu_type: [{ required: true, message: '请选择菜单类型', trigger: 'change' }],
  is_link: [{ required: true, message: '请选择链接类型', trigger: 'change' }],
  sort: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  menu_desc: [{ required: true, message: '请输入菜单说明', trigger: 'blur' }]
}

// 生成菜单ID
const generateMenuId = () => {
  return `MENU${String(++menuIdCounter).padStart(3, '0')}`
}

// 构建查询参数
const buildApiQueryParams = () => {
  const params: Record<string, string> = {}

  // 模糊查询菜单名称
  if (queryForm.menuName) {
    params['menu_name'] = `like.*${queryForm.menuName}*`
  }

  // 排序：默认按照sort升序
  params['order'] = 'sort.asc,menu_id.asc'

  return buildQueryParams(params)
}

// 查询
const handleQuery = () => {
  fetchData()
}

// 重置
const resetQuery = () => {
  Object.assign(queryForm, {
    menuName: ''
  })
  fetchData()
}

// 导出
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 加载数据
const fetchData = async () => {
  loading.value = true
  try {
    const queryString = buildApiQueryParams()
    // 使用PostgREST API
    const url = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_MENU)}?${queryString}`

    const response = await axios.get(url)
    const data = response.data || []

    // 构建树形结构
    tableData.value = buildMenuTree(data)

  } catch (error) {
    console.error('Error fetching data:', error)
    ElMessage.error('数据加载失败，请稍后重试')
    tableData.value = []
  } finally {
    loading.value = false
  }
}

// 构建菜单树形结构
const buildMenuTree = (menuList: BzjMenu[]): BzjMenu[] => {
  const menuMap = new Map<string, BzjMenu>()
  const rootMenus: BzjMenu[] = []

  // 创建菜单映射
  menuList.forEach(menu => {
    menuMap.set(menu.menu_id, { ...menu, children: [] })
  })

  // 构建树形结构
  menuList.forEach(menu => {
    const menuItem = menuMap.get(menu.menu_id)!
    if (menu.parent_id && menuMap.has(menu.parent_id)) {
      const parent = menuMap.get(menu.parent_id)!
      if (!parent.children) parent.children = []
      parent.children.push(menuItem)
    } else {
      rootMenus.push(menuItem)
    }
  })

  return rootMenus
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增菜单'
  isAddChild.value = false
  resetForm()
  formData.menu_id = generateMenuId()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row: BzjMenu) => {
  dialogTitle.value = '修改菜单'
  isAddChild.value = false
  Object.assign(formData, {
    menu_id: row.menu_id,
    parent_id: row.parent_id,
    menu_name: row.menu_name,
    is_link: row.is_link,
    link_url: row.link_url || '',
    menu_type: row.menu_type,
    menu_desc: row.menu_desc || '',
    sort: row.sort || 0
  })
  dialogVisible.value = true
}

// 添加下级菜单
const handleAddChild = (row: BzjMenu) => {
  dialogTitle.value = '添加菜单'
  isAddChild.value = true
  resetForm()
  formData.parent_id = row.menu_id
  formData.menu_id = generateMenuId()
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: BzjMenu) => {
  try {
    await ElMessageBox.confirm(`确认要删除菜单[${row.menu_name}]？`, '提示', {
      confirmButtonText: '是',
      cancelButtonText: '否',
      type: 'warning'
    })

    // 调用API删除数据
    const deleteUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_MENU)}?menu_id=eq.${row.menu_id}`
    await axios.delete(deleteUrl)

    ElMessage.success('删除成功')

    // 重新加载数据
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting data:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构建请求数据
    const requestData = {
      menu_id: formData.menu_id,
      menu_name: formData.menu_name,
      is_link: formData.is_link,
      link_url: formData.link_url || null,
      parent_id: formData.parent_id || null,
      menu_type: formData.menu_type,
      menu_desc: formData.menu_desc || null,
      sort: formData.sort
    }

    if (isAddChild.value || dialogTitle.value === '新增菜单') {
      // 新增或添加下级菜单
      await axios.post(`${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_MENU)}`, requestData)
      ElMessage.success('保存成功')
    } else {
      // 修改
      const updateUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_MENU)}?menu_id=eq.${formData.menu_id}`
      await axios.patch(updateUrl, requestData)
      ElMessage.success('修改成功')
    }

    dialogVisible.value = false
    resetForm()
    // 重新加载数据
    fetchData()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    menu_id: '',
    parent_id: null,
    menu_name: '',
    is_link: '0',
    link_url: '',
    menu_type: 'C',
    menu_desc: '',
    sort: 0
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 关闭对话框
const handleDialogClose = () => {
  resetForm()
}

onMounted(() => {
  // 初始化数据
  fetchData()
})
</script>

<style scoped>
.menu-management {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
