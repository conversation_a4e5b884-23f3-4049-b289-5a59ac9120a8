# 用户管理功能测试案例文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2024-01-15
- **测试页面**: 用户管理 (src/pages/user-management.vue)
- **API接口**: http://172.16.131.94:7300/bzj_user

## 测试环境准备

### 前置条件
1. 确保PostgreSQL数据库服务正常运行
2. 确保RESTful API服务 (http://172.16.131.94:7300) 可访问
3. 浏览器已打开系统并登录
4. 导航到"系统设置" -> "用户管理"页面

### 测试数据准备
建议在数据库中准备以下测试数据：
```sql
-- 测试用户数据
INSERT INTO bzj_user VALUES 
('CJQH', '长江期货', 'DEPT001', '风控部', 'EMP001', 'test001', '张三', '13800138001', '是', 'POST001', '风控专员', '每晚批量同步'),
('CJZQ', '长江证券', 'DEPT002', '业务部', 'EMP002', 'test002', '李四', '13800138002', '否', 'POST002', '业务员', '临时人工授权'),
('CJQH', '长江期货', 'DEPT003', '技术部', 'EMP003', 'test003', '王五', '13800138003', '是', 'POST003', '系统管理员', '每晚批量同步');
```

## 测试案例

### TC001 - 页面加载测试

**测试目的**: 验证用户管理页面能正常加载并显示数据

**测试步骤**:
1. 点击左侧菜单"系统设置" -> "用户管理"
2. 观察页面加载状态

**预期结果**:
- 页面正常加载，显示查询区域和表格区域
- 表格显示现有用户数据
- 分页组件正常显示
- 加载过程中显示loading状态

**验证要点**:
- 表格列标题正确：组织名称、部门编码、所属部门、OA账号、OA名称、员工编码、所属角色、手机号码、访问权限、数据来源、操作
- 数据正确显示，访问权限显示为绿色"是"或红色"否"标签

---

### TC002 - 查询功能测试

#### TC002-1 单条件查询

**测试目的**: 验证单个查询条件的查询功能

**测试步骤**:
1. 在"OA账号"输入框输入"test001"
2. 点击"查询"按钮
3. 观察查询结果

**预期结果**:
- 表格只显示OA账号包含"test001"的用户
- 分页信息正确更新

#### TC002-2 多条件组合查询

**测试目的**: 验证多个查询条件组合查询功能

**测试步骤**:
1. 选择"组织名称"为"长江期货"
2. 在"OA名称"输入框输入"张"
3. 选择"数据来源"为"每晚批量同步"
4. 点击"查询"按钮

**预期结果**:
- 表格显示同时满足所有条件的用户
- 查询结果准确

#### TC002-3 部门多选查询

**测试目的**: 验证部门多选查询功能

**测试步骤**:
1. 点击"所属部门"下拉框
2. 选择多个部门（如"风控部"和"业务部"）
3. 点击"查询"按钮

**预期结果**:
- 表格显示所属部门为选中部门之一的用户
- 多选功能正常工作

#### TC002-4 重置查询

**测试目的**: 验证重置查询功能

**测试步骤**:
1. 填写任意查询条件
2. 点击"重置"按钮
3. 观察页面变化

**预期结果**:
- 所有查询条件清空
- 表格显示所有用户数据
- 分页重置到第一页

---

### TC003 - 新增用户功能测试

#### TC003-1 正常新增用户

**测试目的**: 验证新增用户功能

**测试步骤**:
1. 点击"新增"按钮
2. 填写表单信息：
   - 组织名称：选择"长江期货"
   - 部门编码：输入"DEPT004"
   - 所属部门：选择"测试部"
   - OA账号：输入"test004"（确保唯一）
   - OA名称：输入"测试用户"
   - 员工编码：输入"EMP004"
   - 手机号码：输入"13800138004"
   - 访问权限：选择"是"
   - 数据来源：选择"临时人工授权"
3. 点击"保存"按钮

**预期结果**:
- 弹出"新增成功"提示
- 对话框关闭
- 表格自动刷新，显示新增的用户
- 新用户信息正确显示

#### TC003-2 必填字段验证

**测试目的**: 验证必填字段验证功能

**测试步骤**:
1. 点击"新增"按钮
2. 不填写"OA账号"字段
3. 点击"保存"按钮

**预期结果**:
- 显示"请输入OA账号"警告提示
- 保存按钮保持禁用状态
- 对话框不关闭

#### TC003-3 手机号格式验证

**测试目的**: 验证手机号格式验证功能

**测试步骤**:
1. 点击"新增"按钮
2. 填写基本信息，手机号码输入"123456"（错误格式）
3. 点击"保存"按钮

**预期结果**:
- 显示"请输入正确的手机号格式"错误提示
- 表单验证失败，无法保存

#### TC003-4 OA账号唯一性验证

**测试目的**: 验证OA账号唯一性

**测试步骤**:
1. 点击"新增"按钮
2. 填写表单，OA账号输入已存在的"test001"
3. 点击"保存"按钮

**预期结果**:
- 显示相关错误提示（数据库约束错误）
- 新增失败

---

### TC004 - 修改用户功能测试

#### TC004-1 正常修改用户

**测试目的**: 验证修改用户功能

**测试步骤**:
1. 在表格中找到要修改的用户
2. 点击该用户行的"修改"按钮
3. 修改用户信息（如修改OA名称为"张三-修改"）
4. 点击"保存"按钮

**预期结果**:
- 弹出"修改成功"提示
- 对话框关闭
- 表格自动刷新，显示修改后的信息
- OA账号字段显示为禁用状态（不可修改）

#### TC004-2 OA账号不可修改验证

**测试目的**: 验证修改时OA账号不可修改

**测试步骤**:
1. 点击任意用户的"修改"按钮
2. 观察OA账号字段状态

**预期结果**:
- OA账号字段显示为禁用状态
- 无法修改OA账号内容

---

### TC005 - 删除用户功能测试

#### TC005-1 正常删除用户

**测试目的**: 验证删除用户功能

**测试步骤**:
1. 在表格中找到要删除的用户
2. 点击该用户行的"删除"按钮
3. 在确认对话框中点击"是"

**预期结果**:
- 弹出确认对话框，内容为"确认要删除用户[用户名称]？"
- 点击"是"后显示"删除成功"提示
- 表格自动刷新，该用户不再显示

#### TC005-2 取消删除

**测试目的**: 验证取消删除功能

**测试步骤**:
1. 点击任意用户的"删除"按钮
2. 在确认对话框中点击"否"

**预期结果**:
- 对话框关闭
- 用户未被删除，仍在表格中显示

---

### TC006 - 角色管理功能测试

#### TC006-1 查看用户角色

**测试目的**: 验证查看用户角色功能

**测试步骤**:
1. 点击任意用户的"角色"按钮
2. 观察角色配置对话框

**预期结果**:
- 弹出"绑定用户角色"对话框
- 显示角色列表：角色编号、角色名称、是否主角色
- 已有角色显示为选中状态

#### TC006-2 修改用户角色

**测试目的**: 验证修改用户角色功能

**测试步骤**:
1. 点击用户的"角色"按钮
2. 勾选/取消勾选角色
3. 设置主角色
4. 点击"保存"按钮

**预期结果**:
- 角色选择正常工作
- 主角色只能在已选中的角色中选择
- 保存成功后显示"角色绑定成功"提示

---

### TC007 - 分页功能测试

#### TC007-1 分页导航

**测试目的**: 验证分页导航功能

**测试步骤**:
1. 确保数据量超过一页
2. 点击分页组件的"下一页"按钮
3. 点击具体页码
4. 点击"上一页"按钮

**预期结果**:
- 分页导航正常工作
- 表格数据正确切换
- 当前页码正确显示

#### TC007-2 页大小调整

**测试目的**: 验证页大小调整功能

**测试步骤**:
1. 点击页大小下拉框
2. 选择不同的页大小（如20条/页）
3. 观察表格变化

**预期结果**:
- 表格显示的记录数按新的页大小调整
- 分页信息正确更新
- 自动跳转到第一页

---

### TC008 - 导出功能测试

**测试目的**: 验证导出功能

**测试步骤**:
1. 点击"导出"按钮
2. 观察系统响应

**预期结果**:
- 显示"导出成功"提示
- （实际项目中应该下载Excel文件）

---

### TC009 - 异常情况测试

#### TC009-1 网络异常测试

**测试目的**: 验证网络异常时的处理

**测试步骤**:
1. 断开网络连接或停止API服务
2. 执行查询、新增、修改、删除操作
3. 观察系统响应

**预期结果**:
- 显示相应的错误提示
- 系统不崩溃，用户体验良好

#### TC009-2 数据格式异常测试

**测试目的**: 验证数据格式异常处理

**测试步骤**:
1. 输入特殊字符或超长文本
2. 尝试保存
3. 观察系统响应

**预期结果**:
- 系统正确处理异常数据
- 显示适当的错误提示

---

## 测试检查清单

### 功能完整性检查
- [ ] 页面加载正常
- [ ] 查询功能完整（单条件、多条件、重置）
- [ ] 新增功能完整（表单验证、数据保存）
- [ ] 修改功能完整（数据回填、保存更新）
- [ ] 删除功能完整（确认对话框、数据删除）
- [ ] 角色管理功能完整
- [ ] 分页功能完整
- [ ] 导出功能正常

### 数据验证检查
- [ ] 必填字段验证
- [ ] 数据格式验证（手机号等）
- [ ] 唯一性验证（OA账号）
- [ ] 数据长度限制

### 用户体验检查
- [ ] 加载状态显示
- [ ] 操作反馈及时
- [ ] 错误提示友好
- [ ] 界面布局合理

### 性能检查
- [ ] 页面加载速度
- [ ] 查询响应速度
- [ ] 大数据量处理

## 已知问题记录

| 问题编号 | 问题描述 | 严重程度 | 状态 |
|---------|---------|---------|------|
| - | - | - | - |

## 测试总结

测试完成后，请在此处记录：
- 测试执行情况
- 发现的问题
- 测试结论
- 改进建议

---

**注意事项**:
1. 测试过程中请注意保存测试数据，避免影响其他测试
2. 如发现问题，请详细记录复现步骤
3. 建议在不同浏览器中进行兼容性测试
4. 测试完成后请清理测试数据
