<template>
  <el-config-provider namespace="ep">
    <BaseHeader />
    <div class="main-container flex">
      <div class="sidebar-container">
        <BaseSide />
      </div>
      <div class="content-container">
        <RouterView />
      </div>
    </div>
  </el-config-provider>
</template>

<style>
#app {
  text-align: center;
  color: var(--ep-text-color-primary);
}

.main-container {
  height: calc(100vh - var(--ep-menu-item-height) - 4px);
}

.sidebar-container {
  width: 240px;
  height: 100%;
}

.content-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}
</style>
