<template>
  <div class="param-config">
    <h2>特保申请参数配置</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="参数代码">
              <el-input v-model="queryForm.code" placeholder="请输入参数代码" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="参数名称">
              <el-input v-model="queryForm.name" placeholder="请输入参数名称" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button type="success" @click="handleAdd">新增</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)" border style="width: 100%">
      <el-table-column prop="code" label="参数代码" width="120" />
      <el-table-column prop="name" label="参数名称" width="150" />
      <el-table-column label="参数值" min-width="150">
        <template #default="scope">
          <!-- 文档类型显示为可点击链接 -->
          <el-link
            v-if="scope.row.type === '文档类型'"
            type="primary"
            @click="handleDownload(scope.row)"
            underline="always"
          >
            {{ scope.row.value }}
          </el-link>
          <!-- 其他类型正常显示 -->
          <span v-else>{{ scope.row.value }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="参数类型" width="120" />
      <el-table-column prop="enabled" label="是否启用" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
            {{ scope.row.enabled ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="参数描述" min-width="150" />
      <el-table-column prop="creator" label="设置人员" width="100" />
      <el-table-column prop="createTime" label="设置时间" width="160" />
      <el-table-column prop="modifier" label="修改人员" width="100" />
      <el-table-column prop="updateTime" label="修改时间" width="160" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">修改</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length"
          :pager-count="7"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
    >
      <el-form :model="formData" label-width="120px" :rules="rules" ref="formRef">
        <el-form-item label="参数代码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入参数代码" :disabled="dialogType === 'edit'" />
        </el-form-item>
        <el-form-item label="参数名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择参数类型" style="width: 100%">
            <el-option label="时间类型" value="时间类型" />
            <el-option label="下拉框类型" value="下拉框类型" />
            <el-option label="文本框类型" value="文本框类型" />
            <el-option label="文档类型" value="文档类型" />
          </el-select>
        </el-form-item>
        <el-form-item label="参数值" prop="value">
          <!-- 时间类型 -->
          <el-date-picker
            v-if="formData.type === '时间类型'"
            v-model="formData.value"
            type="datetime"
            placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />

          <!-- 下拉框类型 -->
          <el-select
            v-else-if="formData.type === '下拉框类型'"
            v-model="formData.value"
            placeholder="请选择"
            style="width: 100%"
            multiple
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option label="自然人" value="自然人" />
            <el-option label="一般法人" value="一般法人" />
            <el-option label="特殊法人" value="特殊法人" />
          </el-select>

          <!-- 文本框类型 -->
          <el-input
            v-else-if="formData.type === '文本框类型'"
            v-model="formData.value"
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
          />

          <!-- 文档类型 -->
          <el-upload
            v-else-if="formData.type === '文档类型'"
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :file-list="fileList"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                请选择文档文件
              </div>
            </template>
          </el-upload>

          <!-- 默认输入框 -->
          <el-input
            v-else
            v-model="formData.value"
            placeholder="请输入参数值"
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="enabled">
          <el-switch v-model="formData.enabled" />
        </el-form-item>
        <el-form-item label="参数描述" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入参数描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, UploadFile } from 'element-plus'
import { formatDateTime } from '~/utils/date-format'

// 查询表单
const queryForm = reactive({
  code: '',
  name: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 表格数据
const tableData = ref([
  {
    id: '1',
    code: 'START_TIME',
    name: '禁止申请开始时间',
    value: '2025-04-30 00:00:00',
    type: '时间类型',
    enabled: true,
    description: '特保申请禁止开始时间',
    creator: 'admin',
    createTime: '2025-05-19 12:00:00',
    modifier: '',
    updateTime: ''
  },
  {
    id: '2',
    code: 'END_TIME',
    name: '禁止申请结束时间',
    value: '2025-05-05 23:59:59',
    type: '时间类型',
    enabled: true,
    description: '特保申请禁止结束时间',
    creator: 'admin',
    createTime: '2025-05-19 12:00:00',
    modifier: '',
    updateTime: ''
  },
  {
    id: '3',
    code: 'CLIENT_TYPE',
    name: '禁止申请客户类型',
    value: '自然人,一般法人',
    type: '下拉框类型',
    enabled: true,
    description: '禁止申请的客户类型',
    creator: 'admin',
    createTime: '2025-05-19 12:00:00',
    modifier: 'manager',
    updateTime: '2025-05-20 09:30:00'
  },
  {
    id: '4',
    code: 'NOTICE_TEXT',
    name: '提示语句',
    value: '2025劳动节前暂停特保申请，暂停时间为:2025-04-30 00:00:00到2025-05-05 23:59:59。暂停受理客户类型为【自然人,一般法人】',
    type: '文本框类型',
    enabled: true,
    description: '申请页面显示的提示文本',
    creator: 'admin',
    createTime: '2025-05-19 12:00:00',
    modifier: '',
    updateTime: ''
  },
  {
    id: '5',
    code: 'NOTICE_TEXT2',
    name: '提示语句2',
    value: '近期行情波动剧烈，暂停受理个人客户特保申请，暂停时间为:2025-04-30 00:00:00~2025-05-05 23:59:59，暂停受理客户类型为【自然人,一般法人】',
    type: '文本框类型',
    enabled: false,
    description: '申请页面显示的第二条提示文本',
    creator: 'admin',
    createTime: '2025-05-19 12:00:00',
    modifier: '',
    updateTime: ''
  },
  {
    id: '6',
    code: 'TEMPLATE_DOC',
    name: '特保表单模板',
    value: 'template.docx',
    type: '文档类型',
    enabled: true,
    description: '特保申请表单模板文档',
    creator: 'admin',
    createTime: '2025-05-19 12:00:00',
    modifier: 'manager',
    updateTime: '2025-05-20 09:30:00'
  }
])

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'edit'
const dialogTitle = computed(() => dialogType.value === 'add' ? '新增参数配置' : '修改参数配置')
const formRef = ref<FormInstance>()
const formData = reactive({
  id: '',
  code: '',
  name: '',
  value: '',
  type: '',
  enabled: true,
  description: ''
})

// 文件上传相关
const fileList = ref<UploadFile[]>([])
const handleFileChange = (file: UploadFile) => {
  formData.value = file.name
}

// 文档下载处理
const handleDownload = (row: any) => {
  // 实际项目中，这里应该调用API获取文档的下载链接
  // 这里模拟下载过程
  ElMessage.success(`正在下载文档: ${row.value}`)

  // 创建一个模拟的下载链接
  // 在实际项目中，这里应该使用真实的文档URL
  const link = document.createElement('a')
  link.href = `#${row.value}` // 实际项目中应该是真实的文档URL
  link.download = row.value
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 表单验证规则
const rules = {
  code: [
    { required: true, message: '请输入参数代码', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入参数名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择参数类型', trigger: 'change' }
  ],
  value: [
    { required: true, message: '请输入参数值', trigger: 'blur' }
  ]
}

// 查询方法
const handleQuery = () => {
  // 实际项目中这里应该调用API进行查询
  ElMessage.success('查询成功')
  // 模拟过滤
  const filteredData = tableData.value.filter(item => {
    const codeMatch = !queryForm.code || item.code.includes(queryForm.code)
    const nameMatch = !queryForm.name || item.name.includes(queryForm.name)
    return codeMatch && nameMatch
  })
  tableData.value = filteredData
}

// 重置查询
const resetQuery = () => {
  queryForm.code = ''
  queryForm.name = ''
  // 重新加载数据
  loadData()
}

// 加载数据
const loadData = () => {
  // 实际项目中这里应该调用API获取数据
  // 这里使用静态数据
  tableData.value = [
    {
      id: '1',
      code: 'START_TIME',
      name: '禁止申请开始时间',
      value: '2025-04-30 00:00:00',
      type: '时间类型',
      enabled: true,
      description: '特保申请禁止开始时间',
      creator: 'admin',
      createTime: '2025-05-19 12:00:00',
      modifier: '',
      updateTime: ''
    },
    {
      id: '2',
      code: 'END_TIME',
      name: '禁止申请结束时间',
      value: '2025-05-05 23:59:59',
      type: '时间类型',
      enabled: true,
      description: '特保申请禁止结束时间',
      creator: 'admin',
      createTime: '2025-05-19 12:00:00',
      modifier: '',
      updateTime: ''
    },
    {
      id: '3',
      code: 'CLIENT_TYPE',
      name: '禁止申请客户类型',
      value: '自然人,一般法人',
      type: '下拉框类型',
      enabled: true,
      description: '禁止申请的客户类型',
      creator: 'admin',
      createTime: '2025-05-19 12:00:00',
      modifier: 'manager',
      updateTime: '2025-05-20 09:30:00'
    },
    {
      id: '4',
      code: 'NOTICE_TEXT',
      name: '提示语句',
      value: '2025劳动节前暂停特保申请，暂停时间为:2025-04-30 00:00:00到2025-05-05 23:59:59。暂停受理客户类型为【自然人,一般法人】',
      type: '文本框类型',
      enabled: true,
      description: '申请页面显示的提示文本',
      creator: 'admin',
      createTime: '2025-05-19 12:00:00',
      modifier: '',
      updateTime: ''
    },
    {
      id: '5',
      code: 'NOTICE_TEXT2',
      name: '提示语句2',
      value: '近期行情波动剧烈，暂停受理个人客户特保申请，暂停时间为:2025-04-30 00:00:00~2025-05-05 23:59:59，暂停受理客户类型为【自然人,一般法人】',
      type: '文本框类型',
      enabled: false,
      description: '申请页面显示的第二条提示文本',
      creator: 'admin',
      createTime: '2025-05-19 12:00:00',
      modifier: '',
      updateTime: ''
    },
    {
      id: '6',
      code: 'TEMPLATE_DOC',
      name: '特保表单模板',
      value: 'template.docx',
      type: '文档类型',
      enabled: true,
      description: '特保申请表单模板文档',
      creator: 'admin',
      createTime: '2025-05-19 12:00:00',
      modifier: 'manager',
      updateTime: '2025-05-20 09:30:00'
    }
  ]
}

// 新增
const handleAdd = () => {
  dialogType.value = 'add'
  formData.id = ''
  formData.code = ''
  formData.name = ''
  formData.value = ''
  formData.type = ''
  formData.enabled = true
  formData.description = ''
  fileList.value = []
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  dialogType.value = 'edit'
  formData.id = row.id
  formData.code = row.code
  formData.name = row.name
  formData.value = row.value
  formData.type = row.type
  formData.enabled = row.enabled
  formData.description = row.description

  // 如果是文档类型，设置文件列表
  if (row.type === '文档类型' && row.value) {
    fileList.value = [{
      name: row.value,
      url: '#'
    }] as UploadFile[]
  } else {
    fileList.value = []
  }

  dialogVisible.value = true
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(
    `确认删除参数代码为 ${row.code} 的记录吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 实际项目中这里应该调用API删除数据
      tableData.value = tableData.value.filter(item => item.id !== row.id)
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
    })
    .catch(() => {
      // 取消删除
    })
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate((valid, fields) => {
    if (valid) {
      // 验证通过
      const now = new Date()
      const timeStr = formatDateTime(now)
      const currentUser = 'current_user' // 实际项目中应该从登录信息获取

      if (dialogType.value === 'add') {
        // 新增
        const newId = String(tableData.value.length + 1)
        tableData.value.push({
          id: newId,
          code: formData.code,
          name: formData.name,
          value: formData.value,
          type: formData.type,
          enabled: formData.enabled,
          description: formData.description,
          creator: currentUser,
          createTime: timeStr,
          modifier: '',
          updateTime: ''
        })
        ElMessage.success('新增成功')
      } else {
        // 修改
        const index = tableData.value.findIndex(item => item.id === formData.id)
        if (index !== -1) {
          const oldData = tableData.value[index]
          tableData.value[index] = {
            ...oldData,
            name: formData.name,
            value: formData.value,
            type: formData.type,
            enabled: formData.enabled,
            description: formData.description,
            modifier: currentUser,
            updateTime: timeStr
          }
          ElMessage.success('修改成功')
        }
      }

      dialogVisible.value = false
    } else {
      console.log('验证失败', fields)
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.param-config {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}



.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.upload-demo {
  width: 100%;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
