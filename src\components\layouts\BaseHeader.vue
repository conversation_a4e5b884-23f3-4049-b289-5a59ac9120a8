<script lang="ts" setup>
import { toggleDark } from "~/composables/dark";
</script>

<template>
  <el-menu class="el-menu-demo" mode="horizontal" :ellipsis="false" router>
    <el-menu-item index="/">
      <div class="flex items-center justify-center gap-2">
        <div class="text-xl cjfco-logo" />
        <span class="cjfco-title">特殊保证金</span>
      </div>
    </el-menu-item>

    <el-menu-item index="/business">
      <div class="flex items-center justify-center gap-2">
        <span>业务办理</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/data-management">
      <div class="flex items-center justify-center gap-2">
        <span>数据管理</span>
      </div>
    </el-menu-item>
    <el-menu-item index="/user-management">
      <div class="flex items-center justify-center gap-2">
        <span>系统管理</span>
      </div>
    </el-menu-item>

    <el-menu-item h="full" @click="toggleDark()">
      <button
        class="w-full cursor-pointer border-none bg-transparent"
        style="height: var(--ep-menu-item-height)"
      >
        <i inline-flex i="dark:ep-moon ep-sunny" />
      </button>
    </el-menu-item>
  </el-menu>
</template>

<style lang="scss">
.el-menu-demo {
  &.ep-menu--horizontal > .ep-menu-item:nth-child(1) {
    margin-right: 50px;
  }
}

.cjfco-logo {
  width: 152px;
  height: 40px;
  background-image: url("~/assets/images/logo.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.cjfco-title {
  font-size: 24px;
  font-weight: bold;
}
</style>
