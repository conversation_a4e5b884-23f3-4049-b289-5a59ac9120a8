<template>
  <div class="image-archive-container">
    <h2 class="page-title">影像归档--OA特保流程</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="投资者代码">
              <el-input
                v-model="queryForm.investorCode"
                placeholder="支持批量输入多个客户号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保证金率模板代码">
              <el-input
                v-model="queryForm.templateCode"
                placeholder="支持模糊匹配"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="模板类型">
              <el-autocomplete
                v-model="queryForm.templateType"
                :fetch-suggestions="queryTemplateTypes"
                placeholder="输入模糊匹配"
                clearable
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="所属部门">
              <el-autocomplete
                v-model="queryForm.department"
                :fetch-suggestions="queryDepartments"
                placeholder="输入模糊匹配"
                clearable
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="OA确认开始时间">
              <el-date-picker
                v-model="queryForm.oaConfirmTimeStart"
                type="datetime"
                placeholder="选择日期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="OA确认结束时间">
              <el-date-picker
                v-model="queryForm.oaConfirmTimeEnd"
                type="datetime"
                placeholder="选择日期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-dropdown @command="handleExport" style="margin-left: 10px;">
                <el-button type="success">
                  导出数据
                  <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="current">导出当页</el-dropdown-item>
                    <el-dropdown-item command="all">导出所有</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-button
                :type="autoArchiveEnabled ? 'danger' : 'warning'"
                style="margin-left: 10px;"
                @click="toggleAutoArchive"
              >
                批量归档
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
        border
        style="width: 100%"
        :max-height="600"
      >
      <el-table-column prop="investorCode" label="投资者代码" width="120" />
      <el-table-column prop="investorName" label="投资者名称" width="120" />
      <el-table-column prop="templateCode" label="保证金模板代码" width="150" />
      <el-table-column prop="templateName" label="保证金模板名称" min-width="180" />
      <el-table-column prop="department" label="所属部门" width="150" />
      <el-table-column prop="creator" label="流程发起人" width="120" />
      <el-table-column prop="createTime" label="流程创建时间" width="160" />
      <el-table-column prop="pushOaTime" label="推送OA时间" width="160" />
      <el-table-column prop="oaReturnConfirmTime" label="OA返回确认时间" width="160" />
      <el-table-column prop="archiveTime" label="影像归档时间" width="160" />
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            size="small"
            type="success"
            @click="handleArchive(scope.row)"
            :disabled="scope.row.archiveTime !== null"
          >
            影像归档
          </el-button>
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length"
          :pager-count="7"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="流程详情"
      width="70%"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="投资者代码">{{ currentRow?.investorCode }}</el-descriptions-item>
        <el-descriptions-item label="投资者名称">{{ currentRow?.investorName }}</el-descriptions-item>
        <el-descriptions-item label="保证金模板代码">{{ currentRow?.templateCode }}</el-descriptions-item>
        <el-descriptions-item label="保证金模板名称">{{ currentRow?.templateName }}</el-descriptions-item>
        <el-descriptions-item label="所属部门">{{ currentRow?.department }}</el-descriptions-item>
        <el-descriptions-item label="流程发起人">{{ currentRow?.creator }}</el-descriptions-item>
        <el-descriptions-item label="流程创建时间">{{ currentRow?.createTime }}</el-descriptions-item>
        <el-descriptions-item label="推送OA时间">{{ currentRow?.pushOaTime }}</el-descriptions-item>
        <el-descriptions-item label="OA返回确认时间">{{ currentRow?.oaReturnConfirmTime }}</el-descriptions-item>
        <el-descriptions-item label="影像归档时间">{{ currentRow?.archiveTime || '未归档' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 归档确认对话框 -->
    <el-dialog
      v-model="archiveDialogVisible"
      title="确认归档"
      width="30%"
    >
      <span>确认将该流程进行影像归档？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="archiveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmArchive">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量归档确认对话框 -->
    <el-dialog
      v-model="batchArchiveDialogVisible"
      :title="autoArchiveEnabled ? '确认关闭批量归档' : '确认启用批量归档'"
      width="30%"
    >
      <span>{{ autoArchiveEnabled ? '确认关闭批量归档功能？' : '确认启用批量归档功能？启用后系统将自动归档符合条件的流程。' }}</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchArchiveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmToggleAutoArchive">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { formatDateTime } from '~/utils/date-format'

// 定义数据类型接口
interface ArchiveRecord {
  id: string;
  investorCode: string;
  investorName: string;
  templateCode: string;
  templateName: string;
  department: string;
  creator: string;
  createTime: string;
  pushOaTime: string;
  oaReturnConfirmTime: string;
  archiveTime: string | null;
}

// 查询表单
const queryForm = reactive({
  investorCode: '',
  templateCode: '',
  templateType: '',
  department: '',
  oaConfirmTimeStart: '',
  oaConfirmTimeEnd: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框状态
const detailDialogVisible = ref(false)
const archiveDialogVisible = ref(false)
const batchArchiveDialogVisible = ref(false)

// 自动归档状态
const autoArchiveEnabled = ref(false)

// 当前操作的行数据
const currentRow = ref<ArchiveRecord | null>(null)

// 表格数据
const tableData = ref<ArchiveRecord[]>([])

// 自动完成相关
const templateTypes = [
  { value: '加法' },
  { value: '乘法' }
]

const departments = [
  { value: '江汉营业部' },
  { value: '武汉营业部' },
  { value: '上海营业部' },
  { value: '北京营业部' },
  { value: '广州营业部' },
  { value: '深圳营业部' }
]

const queryTemplateTypes = (queryString: string, cb: any) => {
  const results = queryString
    ? templateTypes.filter(type => type.value.toLowerCase().includes(queryString.toLowerCase()))
    : templateTypes
  cb(results)
}

const queryDepartments = (queryString: string, cb: any) => {
  const results = queryString
    ? departments.filter(dept => dept.value.toLowerCase().includes(queryString.toLowerCase()))
    : departments
  cb(results)
}

// 查询方法
const handleQuery = () => {
  // 实际项目中这里应该调用API进行查询
  ElMessage.success('查询成功')
  // 模拟过滤
  const filteredData = mockData.filter(item => {
    const codeMatch = !queryForm.investorCode ||
      queryForm.investorCode.split(',').some(code => item.investorCode.includes(code.trim()))
    const templateCodeMatch = !queryForm.templateCode || item.templateCode.includes(queryForm.templateCode)
    const templateTypeMatch = !queryForm.templateType || item.templateName.includes(queryForm.templateType)
    const deptMatch = !queryForm.department || item.department.includes(queryForm.department)

    // OA确认时间范围匹配
    let oaConfirmTimeMatch = true
    if (queryForm.oaConfirmTimeStart && queryForm.oaConfirmTimeEnd && item.oaReturnConfirmTime) {
      const itemDate = new Date(item.oaReturnConfirmTime).getTime()
      const startDate = new Date(queryForm.oaConfirmTimeStart).getTime()
      const endDate = new Date(queryForm.oaConfirmTimeEnd).getTime()
      oaConfirmTimeMatch = itemDate >= startDate && itemDate <= endDate
    } else if (queryForm.oaConfirmTimeStart && item.oaReturnConfirmTime) {
      const itemDate = new Date(item.oaReturnConfirmTime).getTime()
      const startDate = new Date(queryForm.oaConfirmTimeStart).getTime()
      oaConfirmTimeMatch = itemDate >= startDate
    } else if (queryForm.oaConfirmTimeEnd && item.oaReturnConfirmTime) {
      const itemDate = new Date(item.oaReturnConfirmTime).getTime()
      const endDate = new Date(queryForm.oaConfirmTimeEnd).getTime()
      oaConfirmTimeMatch = itemDate <= endDate
    }

    return codeMatch && templateCodeMatch && templateTypeMatch && deptMatch && oaConfirmTimeMatch
  })

  tableData.value = filteredData
  currentPage.value = 1
}

// 重置查询
const resetQuery = () => {
  // 使用类型安全的方式重置表单
  queryForm.investorCode = ''
  queryForm.templateCode = ''
  queryForm.templateType = ''
  queryForm.department = ''
  queryForm.oaConfirmTimeStart = ''
  queryForm.oaConfirmTimeEnd = ''

  // 重新加载数据
  loadData()
}

// 导出数据
const handleExport = (command: string) => {
  try {
    // 定义要导出的数据
    let dataToExport: ArchiveRecord[] = []

    if (command === 'current') {
      // 导出当前页数据
      dataToExport = tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
      ElMessage.success('导出当页数据成功')
    } else if (command === 'all') {
      // 导出所有数据
      dataToExport = tableData.value
      ElMessage.success('导出所有数据成功')
    }

    // 定义表头
    const headers = [
      '投资者代码', '投资者名称', '保证金模板代码', '保证金模板名称',
      '所属部门', '流程发起人', '流程创建时间', '推送OA时间',
      'OA返回确认时间', '影像归档时间'
    ]

    // 转换数据格式
    const excelData = [headers]

    dataToExport.forEach(item => {
      const row = [
        item.investorCode,
        item.investorName,
        item.templateCode,
        item.templateName,
        item.department,
        item.creator,
        item.createTime,
        item.pushOaTime,
        item.oaReturnConfirmTime,
        item.archiveTime || ''
      ]
      excelData.push(row)
    })

    // 创建工作簿
    const worksheet = XLSX.utils.aoa_to_sheet(excelData)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'OA特保流程影像归档')

    // 设置列宽
    const columnWidths = headers.map((_, index) => {
      if (index === 0 || index === 1) return { wch: 12 } // 投资者代码、名称
      if (index === 2 || index === 3) return { wch: 20 } // 模板代码、名称
      if (index >= 6 && index <= 9) return { wch: 20 } // 时间字段
      return { wch: 15 } // 其他字段
    })
    worksheet['!cols'] = columnWidths

    // 导出文件
    const fileName = command === 'current' ? 'OA特保流程影像归档_当前页.xlsx' : 'OA特保流程影像归档_全部数据.xlsx'
    XLSX.writeFile(workbook, fileName)
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 处理详情按钮点击
const handleDetail = (row: ArchiveRecord) => {
  currentRow.value = row
  detailDialogVisible.value = true
}

// 处理归档按钮点击
const handleArchive = (row: ArchiveRecord) => {
  currentRow.value = row
  archiveDialogVisible.value = true
}

// 确认归档
const confirmArchive = () => {
  if (!currentRow.value) return

  // 实际项目中这里应该调用API进行归档操作
  const currentRowValue = currentRow.value // 创建一个非空引用
  const index = tableData.value.findIndex(item => item.id === currentRowValue.id)

  if (index !== -1) {
    const now = new Date()
    tableData.value[index].archiveTime = formatDateTime(now)
  }

  archiveDialogVisible.value = false
  ElMessage.success('影像归档成功')
}

// 切换自动归档状态
const toggleAutoArchive = () => {
  batchArchiveDialogVisible.value = true
}

// 确认切换自动归档状态
const confirmToggleAutoArchive = () => {
  autoArchiveEnabled.value = !autoArchiveEnabled.value
  batchArchiveDialogVisible.value = false

  if (autoArchiveEnabled.value) {
    ElMessage.success('批量归档功能已启用')
    // 实际项目中这里应该调用API启用自动归档
  } else {
    ElMessage.warning('批量归档功能已关闭')
    // 实际项目中这里应该调用API关闭自动归档
  }
}

// 生成模拟数据
const generateMockData = (): ArchiveRecord[] => {
  const data: ArchiveRecord[] = []
  // const templateTypes = ['加法', '乘法'] // 未使用，已注释
  const floatPoints = ['0', '1', '2', '3']
  const departments = ['江汉营业部', '武汉营业部', '上海营业部', '北京营业部', '广州营业部', '深圳营业部']
  const creators = ['张三', '李四', '王五', '赵六', '钱七', '孙八']

  // 生成50条数据
  for (let i = 0; i < 50; i++) {
    // const templateType = templateTypes[Math.floor(Math.random() * templateTypes.length)]
    const floatPoint = floatPoints[Math.floor(Math.random() * floatPoints.length)]
    const department = departments[Math.floor(Math.random() * departments.length)]
    const creator = creators[Math.floor(Math.random() * creators.length)]

    // 生成随机日期
    const getRandomDate = (start: Date, end: Date) => {
      const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
      return formatDateTime(date)
    }

    const now = new Date()
    const sixMonthsAgo = new Date()
    sixMonthsAgo.setMonth(now.getMonth() - 6)

    const createTime = getRandomDate(sixMonthsAgo, now)
    const pushOaTime = getRandomDate(new Date(createTime), now)
    const oaReturnConfirmTime = getRandomDate(new Date(pushOaTime), now)

    // 随机决定是否已归档
    const isArchived = Math.random() > 0.5
    const archiveTime = isArchived ? getRandomDate(new Date(oaReturnConfirmTime), now) : null

    data.push({
      id: String(i + 1),
      investorCode: `6002${Math.floor(10000 + Math.random() * 90000)}`,
      investorName: `客户${i + 1}`,
      templateCode: `6A00${floatPoint}A`,
      templateName: `广州/上海/大连/郑州/能源/中金+${floatPoint}%`,
      department,
      creator,
      createTime,
      pushOaTime,
      oaReturnConfirmTime,
      archiveTime
    })
  }

  return data
}

// 模拟数据
const mockData = generateMockData()

// 加载数据
const loadData = () => {
  tableData.value = mockData
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.image-archive-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 22px;
  font-weight: bold;
  text-align: center;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}



.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>