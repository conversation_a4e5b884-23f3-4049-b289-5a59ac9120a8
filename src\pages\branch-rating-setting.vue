<template>
  <div class="branch-rating-setting">
    <h2>分支机构分类评级设置</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分支机构代码">
              <el-select
                v-model="queryForm.branchCodes"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="请选择分支机构"
                :remote-method="remoteSearchBranch"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in branchOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span>{{ item.value }} - {{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button type="success" @click="handleAdd">新增</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="effectiveStartDate" label="生效开始日期" width="120" />
        <el-table-column prop="effectiveEndDate" label="生效结束日期" width="120" />
        <el-table-column prop="branchCode" label="分支机构代码" width="120" />
        <el-table-column prop="branchName" label="分支机构名称" min-width="180" />
        <el-table-column prop="ratingLevel" label="分类评定等级" width="120" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="info" size="small" @click="handleHistory(scope.row)">历史记录</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始日期" prop="effectiveStartDate">
              <el-date-picker
                v-model="formData.effectiveStartDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :disabled="isEdit"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束日期" prop="effectiveEndDate">
              <el-date-picker
                v-model="formData.effectiveEndDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分支机构代码" prop="branchCode">
              <el-select
                v-model="formData.branchCode"
                filterable
                remote
                reserve-keyword
                placeholder="请选择分支机构"
                :remote-method="remoteSearchBranch"
                :disabled="isEdit"
                style="width: 100%"
                @change="handleBranchChange"
              >
                <el-option
                  v-for="item in branchOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span>{{ item.value }} - {{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分支机构名称">
              <el-input v-model="formData.branchName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类评定等级" prop="ratingLevel">
              <el-select v-model="formData.ratingLevel" placeholder="请选择评级" style="width: 100%">
                <el-option label="A" value="A" />
                <el-option label="B" value="B" />
                <el-option label="C" value="C" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="历史记录"
      width="900px"
    >
      <el-table :data="historyData" border style="width: 100%">
        <el-table-column prop="effectiveStartDate" label="生效开始日期" width="120" />
        <el-table-column prop="effectiveEndDate" label="生效结束日期" width="120" />
        <el-table-column prop="branchCode" label="分支机构代码" width="120" />
        <el-table-column prop="branchName" label="分支机构名称" min-width="180" />
        <el-table-column prop="ratingLevel" label="分类评定等级" width="120" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'

// 定义分支机构分类评级数据类型
interface BranchRating {
  id?: string
  effectiveStartDate: string
  effectiveEndDate: string
  branchCode: string
  branchName: string
  ratingLevel: string
  createTime: string
  updateTime: string
  isLatest?: boolean
}

// 分支机构选项类型
interface BranchOption {
  value: string
  label: string
}

// 查询表单
const queryForm = reactive({
  branchCodes: [] as string[]
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref<BranchRating[]>([])

// 分支机构选项
const branchOptions = ref<BranchOption[]>([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref<FormInstance>()
const isEdit = ref(false)

// 历史记录对话框
const historyDialogVisible = ref(false)
const historyData = ref<BranchRating[]>([])

// 表单数据
const formData = reactive<BranchRating>({
  effectiveStartDate: '',
  effectiveEndDate: '',
  branchCode: '',
  branchName: '',
  ratingLevel: '',
  createTime: '',
  updateTime: ''
})

// 表单验证规则
const formRules = {
  effectiveStartDate: [
    { required: true, message: '请选择生效开始日期', trigger: 'change' }
  ],
  effectiveEndDate: [
    { required: true, message: '请选择生效结束日期', trigger: 'change' }
  ],
  branchCode: [
    { required: true, message: '请选择分支机构', trigger: 'change' }
  ],
  ratingLevel: [
    { required: true, message: '请选择分类评定等级', trigger: 'change' }
  ]
}

// 模拟数据
const mockBranchOptions = [
  { value: '1001', label: '上海浦东营业部' },
  { value: '1002', label: '上海黄浦营业部' },
  { value: '1033', label: '北京海淀区营业部' },
  { value: '1034', label: '北京朝阳区营业部' },
  { value: '1045', label: '广州天河区营业部' },
  { value: '1046', label: '广州越秀区营业部' },
  { value: '1057', label: '深圳南山区营业部' },
  { value: '1058', label: '深圳福田区营业部' },
  { value: '1069', label: '成都锦江区营业部' },
  { value: '1070', label: '成都武侯区营业部' }
]

// 生成模拟数据
const generateMockData = () => {
  const data: BranchRating[] = []
  
  for (let i = 0; i < 20; i++) {
    const branchIndex = i % mockBranchOptions.length
    const branch = mockBranchOptions[branchIndex]
    const year = 2023 + Math.floor(i / 10)
    
    data.push({
      id: `${i + 1}`,
      effectiveStartDate: `${year}-01`,
      effectiveEndDate: `${year}-12`,
      branchCode: branch.value,
      branchName: branch.label,
      ratingLevel: ['A', 'B', 'C'][i % 3],
      createTime: `${year}-07-01 10:21:22`,
      updateTime: `${year}-07-02 08:11:22`,
      isLatest: true
    })
  }
  
  return data
}

// 初始化数据
onMounted(() => {
  // 初始化分支机构选项
  branchOptions.value = mockBranchOptions
  
  // 初始化表格数据
  const mockData = generateMockData()
  tableData.value = mockData
  totalCount.value = mockData.length
})

// 远程搜索分支机构
const remoteSearchBranch = (query: string) => {
  if (query) {
    const results = mockBranchOptions.filter(item => 
      item.label.includes(query) || item.value.includes(query)
    )
    branchOptions.value = results
  } else {
    branchOptions.value = mockBranchOptions
  }
}

// 处理分支机构选择变更
const handleBranchChange = (value: string) => {
  const selectedBranch = mockBranchOptions.find(item => item.value === value)
  if (selectedBranch) {
    formData.branchName = selectedBranch.label
  }
}

// 查询
const handleQuery = () => {
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    if (queryForm.branchCodes && queryForm.branchCodes.length > 0) {
      const filteredData = generateMockData().filter(item => 
        queryForm.branchCodes.includes(item.branchCode)
      )
      tableData.value = filteredData
      totalCount.value = filteredData.length
    } else {
      const mockData = generateMockData()
      tableData.value = mockData
      totalCount.value = mockData.length
    }
    
    loading.value = false
  }, 500)
}

// 重置查询
const resetQuery = () => {
  queryForm.branchCodes = []
  handleQuery()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增分支机构分类评级'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row: BranchRating) => {
  dialogTitle.value = '修改分支机构分类评级'
  isEdit.value = true
  resetForm()
  
  // 填充表单数据
  Object.assign(formData, row)
  
  dialogVisible.value = true
}

// 查看历史记录
const handleHistory = (row: BranchRating) => {
  // 模拟获取历史记录
  const mockHistory = [
    { ...row },
    {
      ...row,
      ratingLevel: row.ratingLevel === 'A' ? 'B' : 'A',
      updateTime: '2023-06-01 09:15:30',
      isLatest: false
    },
    {
      ...row,
      ratingLevel: row.ratingLevel === 'C' ? 'A' : 'C',
      updateTime: '2023-05-01 14:22:45',
      isLatest: false
    }
  ]
  
  historyData.value = mockHistory
  historyDialogVisible.value = true
}

// 导出
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid) => {
    if (valid) {
      // 模拟API调用
      setTimeout(() => {
        if (isEdit.value) {
          // 更新记录
          const index = tableData.value.findIndex(item => 
            item.branchCode === formData.branchCode && 
            item.effectiveStartDate === formData.effectiveStartDate
          )
          
          if (index !== -1) {
            // 更新时间
            formData.updateTime = new Date().toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            }).replace(/\//g, '-')
            
            tableData.value[index] = { ...formData }
          }
          
          ElMessage.success('修改成功')
        } else {
          // 新增记录
          const now = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }).replace(/\//g, '-')
          
          const newRecord: BranchRating = {
            ...formData,
            id: String(tableData.value.length + 1),
            createTime: now,
            updateTime: now,
            isLatest: true
          }
          
          tableData.value.unshift(newRecord)
          totalCount.value += 1
          
          ElMessage.success('新增成功')
        }
        
        dialogVisible.value = false
      }, 500)
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  Object.assign(formData, {
    effectiveStartDate: '',
    effectiveEndDate: '',
    branchCode: '',
    branchName: '',
    ratingLevel: '',
    createTime: '',
    updateTime: ''
  })
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style scoped>
.branch-rating-setting {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.button-group .el-button {
  margin: 0 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>