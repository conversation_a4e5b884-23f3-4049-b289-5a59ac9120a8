<template>
  <div class="role-management">
    <h2>角色管理</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="角色编码">
              <el-input v-model="queryForm.roleCode" placeholder="请输入角色编码" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="角色名称">
              <el-input v-model="queryForm.roleName" placeholder="请输入角色名称" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button type="success" @click="handleAdd">新增</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="role_code" label="角色编码" width="150" />
        <el-table-column prop="role_name" label="角色名称" width="200" />
        <el-table-column prop="role_desc" label="角色说明" />
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="warning" size="small" @click="handleMenuPermission(scope.row)">菜单权限</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          :pager-count="7"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改角色对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="角色编码" prop="role_code">
          <el-input
            v-model="formData.role_code"
            placeholder="系统自动生成"
            :disabled="true"
          />
        </el-form-item>
        <el-form-item label="角色名称" prop="role_name">
          <el-input v-model="formData.role_name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色说明" prop="role_desc">
          <el-input
            v-model="formData.role_desc"
            type="textarea"
            :rows="3"
            placeholder="请输入角色说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit" :disabled="!formData.role_name">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 菜单配置对话框 -->
    <el-dialog
      v-model="menuPermissionDialogVisible"
      title="菜单配置"
      width="600px"
    >
      <el-tree
        ref="menuTreeRef"
        :data="menuTreeData"
        :props="{ label: 'menu_name', children: 'children' }"
        show-checkbox
        node-key="menu_id"
        :default-checked-keys="checkedMenuIds"
        :check-strictly="true"
        default-expand-all
      >
        <template #default="{ data }">
          <div class="menu-tree-node">
            <span class="menu-name">{{ data.menu_name }}</span>
            <span class="menu-description">{{ data.menu_desc }}</span>
          </div>
        </template>
      </el-tree>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="menuPermissionDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleMenuPermissionSubmit">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import axios from 'axios'
import { getApiUrl, API_CONFIG, buildQueryParams } from '../config/api'

// 定义角色数据类型
interface BzjRole {
  role_code: string
  role_name: string
  role_desc: string | null
}

// 定义菜单数据类型
interface BzjMenu {
  menu_id: string
  menu_name: string
  is_link: string
  link_url: string | null
  parent_id: string | null
  menu_type: string
  menu_desc: string | null
  sort: number | null
  children?: BzjMenu[]
}

// 定义角色菜单关联类型
interface BzjRoleMenu {
  role_code: string
  menu_id: string
}

// 查询表单
const queryForm = reactive({
  roleCode: '',
  roleName: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 生成角色编码的计数器
let roleIdCounter = 6

// 表格数据
const tableData = ref<BzjRole[]>([])

// 当前操作的角色编码
const currentRoleCode = ref('')

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isEdit = ref(false)

// 菜单权限对话框
const menuPermissionDialogVisible = ref(false)
const menuTreeRef = ref()

// 表单数据
const formData = reactive({
  role_code: '',
  role_name: '',
  role_desc: ''
})

// 表单验证规则
const formRules = {
  role_name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value && isEdit.value) {
          // 编辑时检查角色名称是否重复（排除自己）
          const exists = tableData.value.some(role =>
            role.role_name === value && role.role_code !== formData.role_code
          )
          if (exists) {
            callback(new Error('角色名称已存在'))
          } else {
            callback()
          }
        } else if (value && !isEdit.value) {
          // 新增时检查角色名称是否重复
          const exists = tableData.value.some(role => role.role_name === value)
          if (exists) {
            callback(new Error('角色名称已存在'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 菜单树数据
const menuTreeData = ref<BzjMenu[]>([])

// 选中的菜单ID列表
const checkedMenuIds = ref<string[]>([])

// 生成角色编码
const generateRoleCode = () => {
  const code = `ROLE${String(roleIdCounter).padStart(3, '0')}`
  roleIdCounter++
  return code
}

// 构建查询参数
const buildApiQueryParams = () => {
  const params: Record<string, string> = {}

  // 模糊查询
  if (queryForm.roleCode) {
    params['role_code'] = `like.*${queryForm.roleCode}*`
  }
  if (queryForm.roleName) {
    params['role_name'] = `like.*${queryForm.roleName}*`
  }

  // 排序：默认按照role_code升序
  params['order'] = 'role_code.asc'

  // 分页
  params['limit'] = pageSize.value.toString()
  params['offset'] = ((currentPage.value - 1) * pageSize.value).toString()

  return buildQueryParams(params)
}

// 查询
const handleQuery = () => {
  currentPage.value = 1 // 重置到第一页
  fetchData()
}

// 重置
const resetQuery = () => {
  Object.assign(queryForm, {
    roleCode: '',
    roleName: ''
  })
  currentPage.value = 1
  fetchData()
}

// 分页大小改变
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchData()
}

// 当前页改变
const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  fetchData()
}

// 导出
const handleExport = () => {
  ElMessage.success('导出成功')
}

// 加载角色数据
const fetchData = async () => {
  loading.value = true
  try {
    const queryString = buildApiQueryParams()
    // 使用PostgREST API
    const url = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_ROLE)}?${queryString}`

    const response = await axios.get(url)
    tableData.value = response.data || []

    // 获取总数
    const countParams: Record<string, string> = {}
    if (queryForm.roleCode) {
      countParams['role_code'] = `like.*${queryForm.roleCode}*`
    }
    if (queryForm.roleName) {
      countParams['role_name'] = `like.*${queryForm.roleName}*`
    }
    countParams['select'] = 'count'

    const countQueryString = buildQueryParams(countParams)
    const countUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_ROLE)}?${countQueryString}`
    const countResponse = await axios.get(countUrl, {
      headers: {
        'Prefer': 'count=exact'
      }
    })

    // 根据PostgREST的响应格式获取总数
    totalCount.value = parseInt(countResponse.headers['content-range']?.split('/')[1] || '0') ||
                      countResponse.data[0]?.count || 0

  } catch (error) {
    console.error('Error fetching data:', error)
    ElMessage.error('数据加载失败，请稍后重试')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 加载菜单数据
const fetchMenuData = async () => {
  try {
    // 获取所有菜单数据
    const url = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_MENU)}?order=sort.asc,menu_id.asc`
    const response = await axios.get(url)
    const menuList = response.data || []

    // 构建树形结构
    menuTreeData.value = buildMenuTree(menuList)

  } catch (error) {
    console.error('Error fetching menu data:', error)
    ElMessage.error('菜单数据加载失败')
  }
}

// 构建菜单树形结构
const buildMenuTree = (menuList: BzjMenu[]): BzjMenu[] => {
  const menuMap = new Map<string, BzjMenu>()
  const rootMenus: BzjMenu[] = []

  // 创建菜单映射
  menuList.forEach(menu => {
    menuMap.set(menu.menu_id, { ...menu, children: [] })
  })

  // 构建树形结构
  menuList.forEach(menu => {
    const menuItem = menuMap.get(menu.menu_id)!
    if (menu.parent_id && menuMap.has(menu.parent_id)) {
      const parent = menuMap.get(menu.parent_id)!
      if (!parent.children) parent.children = []
      parent.children.push(menuItem)
    } else {
      rootMenus.push(menuItem)
    }
  })

  return rootMenus
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增角色'
  isEdit.value = false
  resetForm()
  formData.role_code = generateRoleCode()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row: BzjRole) => {
  dialogTitle.value = '修改角色'
  isEdit.value = true
  Object.assign(formData, {
    role_code: row.role_code,
    role_name: row.role_name,
    role_desc: row.role_desc || ''
  })
  dialogVisible.value = true
}

// 菜单权限
const handleMenuPermission = async (row: BzjRole) => {
  currentRoleCode.value = row.role_code

  // 加载菜单数据（如果还没有加载）
  if (menuTreeData.value.length === 0) {
    await fetchMenuData()
  }

  // 获取角色已有的菜单权限
  await fetchRoleMenus(row.role_code)

  menuPermissionDialogVisible.value = true
}

// 获取角色菜单权限
const fetchRoleMenus = async (roleCode: string) => {
  try {
    const url = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_ROLE_MENU)}?role_code=eq.${roleCode}`
    const response = await axios.get(url)
    const roleMenus = response.data || []

    // 提取菜单ID列表
    checkedMenuIds.value = roleMenus.map((rm: BzjRoleMenu) => rm.menu_id)

    // 设置树形控件的选中状态
    if (menuTreeRef.value) {
      menuTreeRef.value.setCheckedKeys(checkedMenuIds.value)
    }

  } catch (error) {
    console.error('Error fetching role menus:', error)
    ElMessage.error('获取角色菜单权限失败')
    checkedMenuIds.value = []
  }
}

// 删除
const handleDelete = async (row: BzjRole) => {
  try {
    await ElMessageBox.confirm(`确认要删除角色[${row.role_name}]？`, '提示', {
      confirmButtonText: '是',
      cancelButtonText: '否',
      type: 'warning'
    })

    // 先删除角色菜单关联
    const deleteRoleMenuUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_ROLE_MENU)}?role_code=eq.${row.role_code}`
    await axios.delete(deleteRoleMenuUrl)

    // 再删除角色
    const deleteRoleUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_ROLE)}?role_code=eq.${row.role_code}`
    await axios.delete(deleteRoleUrl)

    ElMessage.success('删除成功')

    // 重新加载数据
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting role:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formData.role_name) {
    ElMessage.warning('请输入角色名称')
    return
  }

  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构建请求数据
    const requestData = {
      role_code: formData.role_code,
      role_name: formData.role_name,
      role_desc: formData.role_desc || null
    }

    if (isEdit.value) {
      // 修改
      const updateUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_ROLE)}?role_code=eq.${formData.role_code}`
      await axios.patch(updateUrl, requestData)
      ElMessage.success('修改成功')
    } else {
      // 新增
      await axios.post(`${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_ROLE)}`, requestData)
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
    resetForm()
    // 重新加载数据
    fetchData()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 提交菜单权限
const handleMenuPermissionSubmit = async () => {
  try {
    // 只获取实际选中的节点，不包括半选状态的节点
    const checkedKeys = menuTreeRef.value?.getCheckedKeys() || []

    // 先删除该角色的所有菜单权限
    const deleteUrl = `https://172.16.131.95/pgrest${API_CONFIG.ENDPOINTS.BZJ_ROLE_MENU}?role_code=eq.${currentRoleCode.value}`
    await axios.delete(deleteUrl)

    // 批量插入新的菜单权限（只保存实际选中的菜单）
    if (checkedKeys.length > 0) {
      const roleMenus = checkedKeys.map((menuId: string) => ({
        role_code: currentRoleCode.value,
        menu_id: menuId
      }))

      await axios.post(`https://172.16.131.95/pgrest${API_CONFIG.ENDPOINTS.BZJ_ROLE_MENU}`, roleMenus)
    }

    ElMessage.success('菜单权限配置成功')
    menuPermissionDialogVisible.value = false
  } catch (error) {
    console.error('Error saving role menus:', error)
    ElMessage.error('菜单权限配置失败，请稍后重试')
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    role_code: '',
    role_name: '',
    role_desc: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 关闭对话框
const handleDialogClose = () => {
  resetForm()
}

onMounted(() => {
  // 初始化数据
  fetchData()
  fetchMenuData()
})
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.menu-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.menu-name {
  font-weight: 500;
  margin-right: 10px;
  min-width: 150px;
}

.menu-description {
  color: #909399;
  font-size: 12px;
}
</style>
