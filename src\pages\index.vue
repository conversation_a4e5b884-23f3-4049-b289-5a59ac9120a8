<template>
  <div class="process-initiation-container">
    <h2 class="page-title">发起流程</h2>

    <!-- 需存档客户确认记录业务 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span class="section-title">需存档客户确认记录业务</span>
        </div>
      </template>
      <div class="button-container">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="(item, index) in archiveRequiredProcesses" :key="index">
            <el-button
              type="primary"
              class="process-button"
              @click="navigateToExternalSystem(item.url)"
            >
              {{ item.name }}
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 无需存档客户确认记录业务 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span class="section-title">无需存档客户确认记录业务</span>
        </div>
      </template>
      <div class="button-container">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="(item, index) in noArchiveRequiredProcesses" :key="index">
            <el-button
              type="success"
              class="process-button"
              @click="navigateToExternalSystem(item.url)"
            >
              {{ item.name }}
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 需存档客户确认记录业务
const archiveRequiredProcesses = ref([
  {
    name: '特殊保证金率申请',
    url: 'https://example.com/special-rate-application'
  },
  {
    name: '手工出入金申请',
    url: 'https://example.com/manual-fund-application'
  },
  {
    name: '(大连/广州)取消自动组合申请',
    url: 'https://example.com/cancel-auto-combination'
  },
  {
    name: '取消通知申请（承诺书）',
    url: 'https://example.com/cancel-notification'
  },
  {
    name: '移仓业务',
    url: 'https://example.com/warehouse-transfer'
  }
])

// 无需存档客户确认记录业务
const noArchiveRequiredProcesses = ref([
  {
    name: '法人客户个性化风控',
    url: 'https://example.com/legal-person-risk-control'
  },
  {
    name: '期货、期权对冲业务',
    url: 'https://example.com/futures-options-hedging'
  },
  {
    name: '大额出入金',
    url: 'https://example.com/large-fund-transfer'
  },
  {
    name: '软件加收手续费设置',
    url: 'https://example.com/software-fee-setting'
  },
  {
    name: '郑州组合申请',
    url: 'https://example.com/zhengzhou-combination'
  }
])

// 跳转到外部系统
const navigateToExternalSystem = (url: string) => {
  // 在实际应用中，这里可能需要进行权限验证或其他处理
  // 为了演示，我们使用 window.open 打开新窗口
  window.open(url, '_blank')

  // 显示提示信息
  ElMessage.info(`正在跳转到外部系统: ${url}`)
}
</script>

<style scoped>
.process-initiation-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  font-size: 24px;
  margin-bottom: 20px;
  text-align: left;
  color: #303133;
}

.section-card {
  margin-bottom: 30px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.button-container {
  padding: 10px 0;
}

.process-button {
  width: 100%;
  margin: 10px 0;
  height: 50px;
  font-size: 14px;
  white-space: normal;
  word-break: break-word;
  line-height: 1.2;
  padding: 10px 15px;
}

@media (max-width: 768px) {
  .process-button {
    margin: 5px 0;
  }
}
</style>
