<template>
  <div class="template-management">
    <h2>特保模板管理</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="保证金模板代码">
              <el-input v-model="queryForm.code" placeholder="请输入模板代码" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保证金模板名称">
              <el-input v-model="queryForm.name" placeholder="请输入模板名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="模板类型">
              <el-select v-model="queryForm.type" placeholder="请选择" clearable style="width: 100%;">
                <el-option label="所有" value="" />
                <el-option label="加法" value="加法" />
                <el-option label="乘法" value="乘法" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-button type="success" @click="handleAdd">新增</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="bzj_code" label="保证金模板代码" width="150" />
        <el-table-column prop="bzj_name" label="保证金模板名称" min-width="200" />
        <el-table-column prop="bzj_type" label="模板类型" width="100" />
        <el-table-column prop="float_drop" label="上浮点数" width="100" />
        <el-table-column prop="creator" label="设置人员" width="100" />
        <el-table-column prop="create_time" label="设置时间" width="160" />
        <el-table-column prop="updator" label="修改人员" width="100" />
        <el-table-column prop="update_time" label="修改时间" width="160" />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          :pager-count="7"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
    >
      <el-form :model="formData" label-width="120px" :rules="rules" ref="formRef">
        <el-form-item label="保证金模板代码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入模板代码" />
        </el-form-item>
        <el-form-item label="保证金模板名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入模板名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import axios from 'axios'
import { getApiUrl, API_CONFIG, buildQueryParams } from '../config/api'

// 定义数据类型
interface BzjTemplate {
  bzj_code: string
  bzj_name: string
  bzj_type: string
  float_drop: string
  creator: string
  create_time: string
  updator: string
  update_time: string
}

// 查询表单
const queryForm = reactive({
  code: '',
  name: '',
  type: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref<BzjTemplate[]>([])

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'edit'
const dialogTitle = computed(() => dialogType.value === 'add' ? '新增保证金模板' : '修改保证金模板')
const formRef = ref<FormInstance>()
const formData = reactive({
  code: '',
  name: '',
  id: ''
})

// 表单验证规则
const rules = {
  code: [
    { required: true, message: '请输入保证金模板代码', trigger: 'blur' },
    { pattern: /^[0-9A-Za-z]+[AM]$/, message: '模板代码格式不正确，必须以A或M结尾', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入保证金模板名称', trigger: 'blur' }
  ]
}

// 构建查询参数
const buildApiQueryParams = () => {
  const params: Record<string, string> = {}

  // 模糊查询
  if (queryForm.code) {
    params['bzj_code'] = `like.*${queryForm.code}*`
  }
  if (queryForm.name) {
    params['bzj_name'] = `like.*${queryForm.name}*`
  }
  // 精确查询
  if (queryForm.type) {
    params['bzj_type'] = `eq.${queryForm.type}`
  }

  // 排序：默认按照create_time降序+bzj_code降序
  params['order'] = 'create_time.desc,bzj_code.desc'

  // 分页
  params['limit'] = pageSize.value.toString()
  params['offset'] = ((currentPage.value - 1) * pageSize.value).toString()

  return buildQueryParams(params)
}

// 查询方法
const handleQuery = () => {
  currentPage.value = 1 // 重置到第一页
  fetchData()
}

// 重置查询
const resetQuery = () => {
  queryForm.code = ''
  queryForm.name = ''
  queryForm.type = ''
  currentPage.value = 1
  fetchData()
}

// 分页大小改变
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchData()
}

// 当前页改变
const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  fetchData()
}

// 加载数据
const fetchData = async () => {
  loading.value = true
  try {
    const queryString = buildApiQueryParams()
    const url = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_TEMPLATE)}?${queryString}`

    const response = await axios.get(url)
    tableData.value = response.data || []

    // 获取总数（需要单独请求，因为PostgreSQL的RESTful API分页时不返回总数）
    // 构建计数查询参数（不包含limit和offset）
    const countParams: Record<string, string> = {}
    if (queryForm.code) {
      countParams['bzj_code'] = `like.*${queryForm.code}*`
    }
    if (queryForm.name) {
      countParams['bzj_name'] = `like.*${queryForm.name}*`
    }
    if (queryForm.type) {
      countParams['bzj_type'] = `eq.${queryForm.type}`
    }
    countParams['select'] = 'count'

    const countQueryString = buildQueryParams(countParams)
    console.log(
      `Count query string: ${countQueryString}`
    )
    const countUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_TEMPLATE)}?${countQueryString}`
    const countResponse = await axios.get(countUrl, {
      headers: {
        'Prefer': 'count=exact'
      }
    })

    // 根据PostgREST的响应格式获取总数
    totalCount.value = parseInt(countResponse.headers['content-range']?.split('/')[1] || '0') ||
                      countResponse.data[0]?.count || 0

  } catch (error) {
    console.error('Error fetching data:', error)
    ElMessage.error('数据加载失败，请稍后重试')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 新增
const handleAdd = () => {
  dialogType.value = 'add'
  formData.code = ''
  formData.name = ''
  formData.id = ''
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  dialogType.value = 'edit'
  formData.code = row.bzj_code
  formData.name = row.bzj_name
  formData.id = row.bzj_code // 使用code作为id
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: BzjTemplate) => {
  try {
    await ElMessageBox.confirm(
      `确认删除模板代码为 ${row.bzj_code} 的记录吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用API删除数据
    const deleteUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_TEMPLATE)}?bzj_code=eq.${row.bzj_code}`
    await axios.delete(deleteUrl)

    ElMessage({
      type: 'success',
      message: '删除成功',
    })

    // 重新加载数据
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting data:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const currentUser = '当前用户' // 实际项目中应该从用户状态中获取
    const now = new Date()
    // 格式化为PostgreSQL兼容的时间格式：YYYY-MM-DDTHH:mm:ssZ
    const timeStr = now.toISOString().replace(/\.\d{3}Z$/, 'Z')

    // 解析模板代码，获取模板类型和上浮点数
    const code = formData.code
    const type = code.endsWith('A') ? '加法' : '乘法'
    const floatPoint = parseInt(code.substring(2, 5)) / 100

    const requestData = {
      bzj_code: formData.code,
      bzj_name: formData.name,
      bzj_type: type,
      float_drop: floatPoint.toFixed(2)
    }

    if (dialogType.value === 'add') {
      // 新增
      const addData = {
        ...requestData,
        creator: currentUser,
        create_time: timeStr,
        updator: null, // 使用null而不是空字符串
        update_time: null // 使用null而不是空字符串
      }

      await axios.post(getApiUrl(API_CONFIG.ENDPOINTS.BZJ_TEMPLATE), addData)
      ElMessage.success('新增成功')
    } else {
      // 修改
      const updateData = {
        ...requestData,
        updator: currentUser,
        update_time: timeStr
      }

      const updateUrl = `${getApiUrl(API_CONFIG.ENDPOINTS.BZJ_TEMPLATE)}?bzj_code=eq.${formData.id}`
      await axios.patch(updateUrl, updateData)
      ElMessage.success('修改成功')
    }

    dialogVisible.value = false
    resetForm()
    // 重新加载数据
    fetchData()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 重置表单
const resetForm = () => {
  formData.code = ''
  formData.name = ''
  formData.id = ''
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.template-management {
  padding: 20px;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>