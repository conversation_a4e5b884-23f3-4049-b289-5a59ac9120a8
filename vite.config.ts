import path from 'node:path'
import Vue from '@vitejs/plugin-vue'

import Unocss from 'unocss/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import VueRouter from 'unplugin-vue-router/vite'

import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      '~/': `${path.resolve(__dirname, 'src')}/`,
    },
  },

  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "~/styles/main.scss" as *; @use "~/styles/dark.scss" as *;`,
        api: 'modern-compiler',
      },
    },
  },
  server: {
    host: true, // 监听所有地址，相当于 '0.0.0.0'
    // host: '0.0.0.0',  // 显式指定监听所有地址（推荐）
    // host: '*************', // 如果你只想监听特定的 IP 地址
    port: 3000, // 可选：指定端口号，默认是 5173
    open: true,  // 可选：自动在浏览器中打开
  },
  plugins: [
    Vue(),
    // https://github.com/posva/unplugin-vue-router
    VueRouter({
      extensions: ['.vue', '.md'],
      dts: 'src/typed-router.d.ts',
    }),

    Components({
      // allow auto load markdown components under `./src/components/`
      extensions: ['vue', 'md'],
      // allow auto import and register components used in markdown
      include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
      resolvers: [
        ElementPlusResolver({
          importStyle: 'sass',
        }),
      ],
      dts: 'src/components.d.ts',
    }),

    // https://github.com/antfu/unocss
    // see uno.config.ts for config
    Unocss(),
  ],
  base: '/special_fund/',
  ssr: {
    // TODO: workaround until they support native ESM
    noExternal: ['element-plus'],
  },
})
