<template>
  <div class="flow-details-container">
    <h2 class="page-title">流程明细--特殊保证金率申请</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="投资者代码">
              <el-input
                v-model="queryForm.investorCode"
                placeholder="支持批量输入多个客户号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保证金率模板代码">
              <el-input
                v-model="queryForm.templateCode"
                placeholder="支持模糊匹配"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="模板类型">
              <el-select
                v-model="queryForm.templateType"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="加法" value="加法" />
                <el-option label="乘法" value="乘法" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="上浮点数">
              <el-select
                v-model="queryForm.floatPoint"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="0%" value="0" />
                <el-option label="1%" value="1" />
                <el-option label="2%" value="2" />
                <el-option label="3%" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="投资者类型">
              <el-select
                v-model="queryForm.investorType"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="自然人" value="自然人" />
                <el-option label="一般法人" value="一般法人" />
                <el-option label="特殊法人" value="特殊法人" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属部门">
              <el-autocomplete
                v-model="queryForm.department"
                :fetch-suggestions="queryDepartments"
                placeholder="输入模糊匹配"
                clearable
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="直销/IB">
              <el-select
                v-model="queryForm.salesType"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="直销" value="直销" />
                <el-option label="IB" value="IB" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="自动审核结果">
              <el-select
                v-model="queryForm.auditResult"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="满足准入1" value="满足准入1" />
                <el-option label="满足准入2" value="满足准入2" />
                <el-option label="不满足准入" value="不满足准入" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="流程创建开始时间">
              <el-date-picker
                v-model="queryForm.createTimeStart"
                type="datetime"
                placeholder="选择日期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="流程创建结束时间">
              <el-date-picker
                v-model="queryForm.createTimeEnd"
                type="datetime"
                placeholder="选择日期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="流转状态">
              <el-select
                v-model="queryForm.flowStatus"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              >
                <el-option label="所有" value="" />
                <el-option label="FIND已创建" value="FIND已创建" />
                <el-option label="FIND已废弃" value="FIND已废弃" />
                <el-option label="已推送APP" value="已推送APP" />
                <el-option label="已上传附件" value="已上传附件" />
                <el-option label="已拒绝" value="已拒绝" />
                <el-option label="已确认" value="已确认" />
                <el-option label="已推送OA" value="已推送OA" />
                <el-option label="OA已废弃" value="OA已废弃" />
                <el-option label="OA已办结" value="OA已办结" />
                <el-option label="已归档" value="已归档" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="OA确认开始时间">
              <el-date-picker
                v-model="queryForm.oaConfirmTimeStart"
                type="datetime"
                placeholder="选择日期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="OA确认结束时间">
              <el-date-picker
                v-model="queryForm.oaConfirmTimeEnd"
                type="datetime"
                placeholder="选择日期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="OA确认月份">
              <el-date-picker
                v-model="queryForm.oaConfirmMonth"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
              <el-dropdown @command="handleExport" style="margin-left: 10px;">
                <el-button type="success">
                  导出数据
                  <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="current">导出当页</el-dropdown-item>
                    <el-dropdown-item command="all">导出所有</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-button type="warning" style="margin-left: 10px;" @click="downloadTemplate">下载模板</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
        border
        style="width: 100%"
        :max-height="600"
      >
      <el-table-column prop="investorCode" label="投资者代码" width="100" fixed="left" />
      <el-table-column prop="investorName" label="投资者名称" width="120" fixed="left" />
      <el-table-column prop="templateCode" label="保证金模板代码" width="150" />
      <el-table-column prop="templateName" label="保证金模板名称" min-width="180" />
      <el-table-column prop="templateType" label="模板类型" width="100" />
      <el-table-column label="上浮点数" width="120">
        <template #default="scope">
          <el-select
            v-if="scope.row.isEditing"
            v-model="scope.row.floatPoint"
            placeholder="请选择"
            style="width: 80px;"
          >
            <el-option label="0%" value="0%" />
            <el-option label="1%" value="1%" />
            <el-option label="2%" value="2%" />
            <el-option label="3%" value="3%" />
          </el-select>
          <span v-else>{{ scope.row.floatPoint }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="investorType" label="投资者类型" width="100" />
      <el-table-column prop="auditResult" label="自动审核结果" width="120" />
      <el-table-column prop="salesType" label="直销/IB" width="100" />
      <el-table-column prop="department" label="所属部门" width="150" />
      <el-table-column prop="creator" label="流程发起人" width="120" />
      <el-table-column prop="createTime" label="流程创建时间" width="160" />
      <el-table-column prop="flowStatus" label="流转状态" width="120" />
      <el-table-column prop="pushAppTime" label="推送APP时间" width="160" />
      <el-table-column prop="uploadAttachmentTime" label="上传附件时间" width="160" />
      <el-table-column prop="appPushUserTime" label="APP推送用户时间" width="160" />
      <el-table-column prop="rejectTime" label="拒绝时间" width="160" />
      <el-table-column prop="confirmTime" label="确认时间" width="160" />
      <el-table-column prop="pushOaTime" label="推送OA时间" width="160" />
      <el-table-column prop="oaReturnConfirmTime" label="OA返回确认时间" width="160" />
      <el-table-column prop="oaConfirmMonth" label="OA确认月份" width="120" />
      <el-table-column label="操作" width="340" fixed="right">
        <template #default="scope">
          <!-- 修改/保存按钮 -->
          <el-button
            size="small"
            type="info"
            @click="handleEdit(scope.row)"
            v-if="!scope.row.isEditing"
          >
            修改
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleSave(scope.row)"
            v-else
          >
            保存
          </el-button>

          <!-- 废弃按钮 -->
          <el-button
            size="small"
            type="danger"
            @click="handleDiscard(scope.row)"
            :disabled="!['FIND已创建', '已推送APP', '已上传附件'].includes(scope.row.flowStatus)"
          >
            废弃
          </el-button>

          <!-- 推送APP按钮 (自然人客户) -->
          <el-button
            v-if="scope.row.investorType === '自然人'"
            size="small"
            type="primary"
            @click="handlePushApp(scope.row)"
            :disabled="scope.row.flowStatus !== 'FIND已创建'"
          >
            推送APP
          </el-button>

          <!-- 上传附件按钮 (法人客户) -->
          <el-button
            v-if="scope.row.investorType !== '自然人'"
            size="small"
            type="warning"
            @click="handleUploadAttachment(scope.row)"
            :disabled="scope.row.flowStatus !== 'FIND已创建'"
          >
            上传附件
          </el-button>

          <!-- 推送OA按钮 -->
          <el-button
            size="small"
            type="success"
            @click="handlePushOa(scope.row)"
            :disabled="scope.row.flowStatus !== '已确认'"
          >
            推送OA
          </el-button>
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length"
          :pager-count="7"
        />
      </div>
    </el-card>

    <!-- 废弃确认对话框 -->
    <el-dialog
      v-model="discardDialogVisible"
      title="确认废弃"
      width="30%"
    >
      <span>确认废弃该特殊保证金率申请流程？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="discardDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmDiscard">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上传附件对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传附件"
      width="50%"
    >
      <el-form :model="uploadForm" label-width="120px">
        <el-form-item label="客户签字附件">
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">请上传客户签字/盖章附件，仅支持jpg/png/pdf文件</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="风控情况说明" required>
          <el-input
            v-model="uploadForm.riskDescription"
            type="textarea"
            :rows="4"
            placeholder="描述客户历史风控情况，客户自己的风控能力，营业部对客户的风险揭示情况。"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="confirmUpload">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 推送OA确认对话框 -->
    <el-dialog
      v-model="pushOaDialogVisible"
      title="确认推送OA"
      width="30%"
    >
      <span>确认将该特殊保证金率申请推送至OA系统？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pushOaDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmPushOa">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { formatDateTime, formatDate, formatMonth } from '~/utils/date-format'

// 查询表单
const queryForm = reactive({
  investorCode: '',
  templateCode: '',
  templateType: '',
  floatPoint: '',
  investorType: '',
  department: '',
  salesType: '',
  auditResult: '',
  createTimeStart: '',
  createTimeEnd: '',
  flowStatus: '',
  oaConfirmTimeStart: '',
  oaConfirmTimeEnd: '',
  oaConfirmMonth: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框状态
const discardDialogVisible = ref(false)
const uploadDialogVisible = ref(false)
const pushOaDialogVisible = ref(false)

// 定义数据类型接口
interface FlowRecord {
  investorCode: string;
  investorName: string;
  templateCode: string;
  templateName: string;
  templateType: string;
  floatPoint: string;
  investorType: string;
  auditResult: string;
  salesType: string;
  department: string;
  creator: string;
  createTime: string;
  flowStatus: string;
  pushAppTime: string | null;
  uploadAttachmentTime: string | null;
  appPushUserTime: string | null;
  rejectTime: string | null;
  confirmTime: string | null;
  pushOaTime: string | null;
  oaReturnConfirmTime: string | null;
  oaConfirmMonth: string | null;
  updateTime?: string;
  isEditing?: boolean; // 添加编辑状态属性
}

// 当前操作的行数据
const currentRow = ref<FlowRecord | null>(null)

// 上传表单
const uploadForm = reactive({
  file: null as any,
  riskDescription: ''
})

// 自动完成相关
const departments = [
  { value: '江汉营业部' },
  { value: '武汉营业部' },
  { value: '上海营业部' },
  { value: '北京营业部' },
  { value: '广州营业部' },
  { value: '深圳营业部' }
]

const queryDepartments = (queryString: string, cb: any) => {
  const results = queryString
    ? departments.filter(dept => dept.value.toLowerCase().includes(queryString.toLowerCase()))
    : departments
  cb(results)
}

// 查询方法
const handleQuery = () => {
  // 实际项目中这里应该调用API进行查询
  ElMessage.success('查询成功')
  // 模拟过滤
  const filteredData = mockData.filter(item => {
    const codeMatch = !queryForm.investorCode ||
      queryForm.investorCode.split(',').some(code => item.investorCode.includes(code.trim()))
    const templateCodeMatch = !queryForm.templateCode || item.templateCode.includes(queryForm.templateCode)
    const typeMatch = !queryForm.templateType || item.templateType === queryForm.templateType
    const floatMatch = !queryForm.floatPoint || item.floatPoint === queryForm.floatPoint
    const investorTypeMatch = !queryForm.investorType || item.investorType === queryForm.investorType
    const deptMatch = !queryForm.department || item.department.includes(queryForm.department)
    const salesTypeMatch = !queryForm.salesType || item.salesType === queryForm.salesType
    const auditResultMatch = !queryForm.auditResult || item.auditResult.includes(queryForm.auditResult)
    const flowStatusMatch = !queryForm.flowStatus || item.flowStatus === queryForm.flowStatus

    // 创建时间范围匹配
    let createTimeMatch = true
    if (queryForm.createTimeStart && queryForm.createTimeEnd) {
      const itemDate = new Date(item.createTime).getTime()
      const startDate = new Date(queryForm.createTimeStart).getTime()
      const endDate = new Date(queryForm.createTimeEnd).getTime()
      createTimeMatch = itemDate >= startDate && itemDate <= endDate
    } else if (queryForm.createTimeStart) {
      const itemDate = new Date(item.createTime).getTime()
      const startDate = new Date(queryForm.createTimeStart).getTime()
      createTimeMatch = itemDate >= startDate
    } else if (queryForm.createTimeEnd) {
      const itemDate = new Date(item.createTime).getTime()
      const endDate = new Date(queryForm.createTimeEnd).getTime()
      createTimeMatch = itemDate <= endDate
    }

    // OA确认时间范围匹配
    let oaConfirmTimeMatch = true
    if (queryForm.oaConfirmTimeStart && queryForm.oaConfirmTimeEnd && item.oaReturnConfirmTime) {
      const itemDate = new Date(item.oaReturnConfirmTime).getTime()
      const startDate = new Date(queryForm.oaConfirmTimeStart).getTime()
      const endDate = new Date(queryForm.oaConfirmTimeEnd).getTime()
      oaConfirmTimeMatch = itemDate >= startDate && itemDate <= endDate
    } else if (queryForm.oaConfirmTimeStart && item.oaReturnConfirmTime) {
      const itemDate = new Date(item.oaReturnConfirmTime).getTime()
      const startDate = new Date(queryForm.oaConfirmTimeStart).getTime()
      oaConfirmTimeMatch = itemDate >= startDate
    } else if (queryForm.oaConfirmTimeEnd && item.oaReturnConfirmTime) {
      const itemDate = new Date(item.oaReturnConfirmTime).getTime()
      const endDate = new Date(queryForm.oaConfirmTimeEnd).getTime()
      oaConfirmTimeMatch = itemDate <= endDate
    }

    // OA确认月份匹配
    let oaConfirmMonthMatch = true
    if (queryForm.oaConfirmMonth && item.oaConfirmMonth) {
      const queryMonth = new Date(queryForm.oaConfirmMonth).toISOString().slice(0, 7)
      oaConfirmMonthMatch = item.oaConfirmMonth.includes(queryMonth)
    }

    return codeMatch && templateCodeMatch && typeMatch && floatMatch && investorTypeMatch &&
           deptMatch && salesTypeMatch && auditResultMatch && flowStatusMatch &&
           createTimeMatch && oaConfirmTimeMatch && oaConfirmMonthMatch
  })

  tableData.value = filteredData
  currentPage.value = 1
}

// 重置查询
const resetQuery = () => {
  // 使用类型安全的方式重置表单
  queryForm.investorCode = ''
  queryForm.templateCode = ''
  queryForm.templateType = ''
  queryForm.floatPoint = ''
  queryForm.investorType = ''
  queryForm.department = ''
  queryForm.salesType = ''
  queryForm.auditResult = ''
  queryForm.createTimeStart = ''
  queryForm.createTimeEnd = ''
  queryForm.flowStatus = ''
  queryForm.oaConfirmTimeStart = ''
  queryForm.oaConfirmTimeEnd = ''
  queryForm.oaConfirmMonth = ''

  // 重新加载数据
  loadData()
}

// 导出数据
const handleExport = (command: string) => {
  try {
    // 定义要导出的数据
    let dataToExport: FlowRecord[] = []

    if (command === 'current') {
      // 导出当前页数据
      dataToExport = tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
      ElMessage.success('导出当页数据成功')
    } else if (command === 'all') {
      // 导出所有数据
      dataToExport = tableData.value
      ElMessage.success('导出所有数据成功')
    }

    // 定义表头
    const headers = [
      '投资者代码', '投资者名称', '保证金模板代码', '保证金模板名称', '模板类型',
      '上浮点数', '投资者类型', '自动审核结果', '直销/IB', '所属部门',
      '流程发起人', '流程创建时间', '流转状态', '推送APP时间', '上传附件时间',
      'APP推送用户时间', '拒绝时间', '确认时间', '推送OA时间', 'OA返回确认时间',
      'OA确认月份'
    ]

    // 转换数据格式
    const excelData = [headers]

    dataToExport.forEach(item => {
      const row = [
        item.investorCode,
        item.investorName,
        item.templateCode,
        item.templateName,
        item.templateType,
        item.floatPoint,
        item.investorType,
        item.auditResult,
        item.salesType,
        item.department,
        item.creator,
        item.createTime,
        item.flowStatus,
        item.pushAppTime || '',
        item.uploadAttachmentTime || '',
        item.appPushUserTime || '',
        item.rejectTime || '',
        item.confirmTime || '',
        item.pushOaTime || '',
        item.oaReturnConfirmTime || '',
        item.oaConfirmMonth || ''
      ]
      excelData.push(row)
    })

    // 创建工作簿
    const worksheet = XLSX.utils.aoa_to_sheet(excelData)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, '特殊保证金率申请流程明细')

    // 设置列宽
    const columnWidths = headers.map((_, index) => {
      if (index === 0 || index === 1) return { wch: 12 } // 投资者代码、名称
      if (index === 2 || index === 3) return { wch: 20 } // 模板代码、名称
      if (index >= 11 && index <= 19) return { wch: 20 } // 时间字段
      return { wch: 15 } // 其他字段
    })
    worksheet['!cols'] = columnWidths

    // 导出文件
    const fileName = command === 'current' ? '特殊保证金率申请流程明细_当前页.xlsx' : '特殊保证金率申请流程明细_全部数据.xlsx'
    XLSX.writeFile(workbook, fileName)
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 下载模板
const downloadTemplate = () => {
  // 实际项目中这里应该调用API下载模板
  ElMessage.success('模板下载成功')
}

// 处理废弃按钮点击
const handleDiscard = (row: FlowRecord) => {
  currentRow.value = row
  discardDialogVisible.value = true
}

// 确认废弃
const confirmDiscard = () => {
  if (!currentRow.value) return

  // 实际项目中这里应该调用API进行废弃操作
  const currentRowValue = currentRow.value // 创建一个非空引用
  const index = tableData.value.findIndex(item =>
    item.investorCode === currentRowValue.investorCode &&
    item.createTime === currentRowValue.createTime
  )

  if (index !== -1) {
    tableData.value[index].flowStatus = 'FIND已废弃'
    const now = new Date()
    tableData.value[index].updateTime = formatDateTime(now)
  }

  discardDialogVisible.value = false
  ElMessage.success('流程已成功废弃')
}

// 这些方法已不再使用，因为我们直接在模板中使用了条件判断
// 保留注释以便于理解之前的逻辑

// 处理推送APP按钮点击
const handlePushApp = (row: FlowRecord) => {
  currentRow.value = row

  // 实际项目中这里应该调用API进行推送APP操作
  ElMessageBox.confirm(
    '确认将该特殊保证金率申请推送至APP？',
    '确认推送',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      if (!currentRow.value) return

      const currentRowValue = currentRow.value // 创建一个非空引用
      const index = tableData.value.findIndex(item =>
        item.investorCode === currentRowValue.investorCode &&
        item.createTime === currentRowValue.createTime
      )

      if (index !== -1) {
        tableData.value[index].flowStatus = '已推送APP'
        const now = new Date()
        tableData.value[index].pushAppTime = formatDateTime(now)
      }

      ElMessage.success('已成功推送至APP')
    })
    .catch(() => {
      // 取消操作
    })
}

// 处理上传附件按钮点击
const handleUploadAttachment = (row: FlowRecord) => {
  currentRow.value = row
  uploadForm.file = null
  uploadForm.riskDescription = ''
  uploadDialogVisible.value = true
}

// 处理文件变更
const handleFileChange = (file: any) => {
  uploadForm.file = file
}

// 确认上传
const confirmUpload = () => {
  if (!uploadForm.riskDescription) {
    ElMessage.warning('请填写风控情况说明')
    return
  }

  if (!uploadForm.file) {
    ElMessage.warning('请上传客户签字/盖章附件')
    return
  }

  if (!currentRow.value) {
    ElMessage.warning('未找到当前操作的记录')
    return
  }

  // 实际项目中这里应该调用API进行上传操作
  const currentRowValue = currentRow.value // 创建一个非空引用
  const index = tableData.value.findIndex(item =>
    item.investorCode === currentRowValue.investorCode &&
    item.createTime === currentRowValue.createTime
  )

  if (index !== -1) {
    tableData.value[index].flowStatus = '已上传附件'
    const now = new Date()
    tableData.value[index].uploadAttachmentTime = formatDateTime(now)
  }

  uploadDialogVisible.value = false
  ElMessage.success('附件上传成功')
}

// 处理推送OA按钮点击
const handlePushOa = (row: FlowRecord) => {
  currentRow.value = row
  pushOaDialogVisible.value = true
}

// 确认推送OA
const confirmPushOa = () => {
  if (!currentRow.value) return

  // 实际项目中这里应该调用API进行推送OA操作
  const currentRowValue = currentRow.value // 创建一个非空引用
  const index = tableData.value.findIndex(item =>
    item.investorCode === currentRowValue.investorCode &&
    item.createTime === currentRowValue.createTime
  )

  if (index !== -1) {
    tableData.value[index].flowStatus = '已推送OA'
    const now = new Date()
    tableData.value[index].pushOaTime = formatDateTime(now)
  }

  pushOaDialogVisible.value = false
  ElMessage.success('已成功推送至OA')
}

// 表格数据
const tableData = ref<FlowRecord[]>([])

// 处理编辑按钮点击
const handleEdit = (row: FlowRecord) => {
  // 设置行为编辑状态
  row.isEditing = true
}

// 处理保存按钮点击
const handleSave = (row: FlowRecord) => {
  // 保存编辑内容
  row.isEditing = false

  // 实际项目中这里应该调用API进行保存操作
  ElMessage.success('上浮点数修改成功')

  // 如果需要，可以在这里更新其他相关字段
  // 例如：更新模板名称以反映新的上浮点数
  const floatValue = row.floatPoint.replace('%', '')
  row.templateName = `广州/上海/大连/郑州/能源/中金+${floatValue}%`
  row.templateCode = `6A00${floatValue}A`
}

// 生成模拟数据
const generateMockData = (): FlowRecord[] => {
  const data = []
  const investorTypes = ['自然人', '一般法人', '特殊法人']
  const templateTypes = ['加法', '乘法']
  const floatPoints = ['0', '1', '2', '3']
  const auditResults = ['满足准入1', '满足准入2', '不满足准入']
  const salesTypes = ['直销', 'IB']
  const departments = ['江汉营业部', '武汉营业部', '上海营业部', '北京营业部', '广州营业部', '深圳营业部']
  const flowStatuses = [
    'FIND已创建', 'FIND已废弃', '已推送APP', '已上传附件',
    '已拒绝', '已确认', '已推送OA', 'OA已废弃', 'OA已办结', '已归档'
  ]
  const creators = ['张三', '李四', '王五', '赵六', '钱七', '孙八']

  // 生成50条数据
  for (let i = 0; i < 50; i++) {
    const investorType = investorTypes[Math.floor(Math.random() * investorTypes.length)]
    const templateType = templateTypes[Math.floor(Math.random() * templateTypes.length)]
    const floatPoint = floatPoints[Math.floor(Math.random() * floatPoints.length)]
    const auditResult = auditResults[Math.floor(Math.random() * auditResults.length)]
    const salesType = salesTypes[Math.floor(Math.random() * salesTypes.length)]
    const department = departments[Math.floor(Math.random() * departments.length)]
    const flowStatus = flowStatuses[Math.floor(Math.random() * flowStatuses.length)]
    const creator = creators[Math.floor(Math.random() * creators.length)]

    // 生成随机日期
    const getRandomDate = (start: Date, end: Date) => {
      const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
      return formatDateTime(date)
    }

    const now = new Date()
    const sixMonthsAgo = new Date()
    sixMonthsAgo.setMonth(now.getMonth() - 6)

    const createTime = getRandomDate(sixMonthsAgo, now)

    // 根据流转状态生成相应的时间
    let pushAppTime = null
    let uploadAttachmentTime = null
    let appPushUserTime = null
    let rejectTime = null
    let confirmTime = null
    let pushOaTime = null
    let oaReturnConfirmTime = null
    let oaConfirmMonth = null

    if (['已推送APP', '已拒绝', '已确认', '已推送OA', 'OA已废弃', 'OA已办结', '已归档'].includes(flowStatus)) {
      pushAppTime = getRandomDate(new Date(createTime), now)
    }

    if (['已上传附件', '已确认', '已推送OA', 'OA已废弃', 'OA已办结', '已归档'].includes(flowStatus)) {
      uploadAttachmentTime = getRandomDate(new Date(createTime), now)
    }

    if (['已推送APP', '已拒绝', '已确认', '已推送OA', 'OA已废弃', 'OA已办结', '已归档'].includes(flowStatus)) {
      appPushUserTime = pushAppTime ? getRandomDate(new Date(pushAppTime), now) : null
    }

    if (flowStatus === '已拒绝') {
      rejectTime = appPushUserTime ? getRandomDate(new Date(appPushUserTime), now) : null
    }

    if (['已确认', '已推送OA', 'OA已废弃', 'OA已办结', '已归档'].includes(flowStatus)) {
      confirmTime = appPushUserTime ? getRandomDate(new Date(appPushUserTime), now) :
                    uploadAttachmentTime ? getRandomDate(new Date(uploadAttachmentTime), now) : null
    }

    if (['已推送OA', 'OA已废弃', 'OA已办结', '已归档'].includes(flowStatus)) {
      pushOaTime = confirmTime ? getRandomDate(new Date(confirmTime), now) : null
    }

    if (['OA已办结', '已归档'].includes(flowStatus)) {
      oaReturnConfirmTime = pushOaTime ? getRandomDate(new Date(pushOaTime), now) : null
      if (oaReturnConfirmTime) {
        const date = new Date(oaReturnConfirmTime)
        oaConfirmMonth = formatMonth(date)
      }
    }

    data.push({
      investorCode: `6002${Math.floor(10000 + Math.random() * 90000)}`,
      investorName: `客户${i + 1}`,
      templateCode: `6A00${floatPoint}A`,
      templateName: `广州/上海/大连/郑州/能源/中金+${floatPoint}%`,
      templateType,
      floatPoint: `${floatPoint}%`,
      investorType,
      auditResult,
      salesType,
      department,
      creator,
      createTime,
      flowStatus,
      pushAppTime,
      uploadAttachmentTime,
      appPushUserTime,
      rejectTime,
      confirmTime,
      pushOaTime,
      oaReturnConfirmTime,
      oaConfirmMonth,
      isEditing: false // 初始状态为非编辑状态
    })
  }

  return data
}

// 模拟数据
const mockData = generateMockData()

// 加载数据
const loadData = () => {
  tableData.value = mockData
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.flow-details-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 22px;
  font-weight: bold;
  text-align: center;
}

.query-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.query-form {
  margin: 0;
}



.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}
</style>


