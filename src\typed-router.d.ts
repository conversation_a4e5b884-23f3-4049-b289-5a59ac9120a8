/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/branch-rating-setting': RouteRecordInfo<'/branch-rating-setting', '/branch-rating-setting', Record<never, never>, Record<never, never>>,
    '/custom-query': RouteRecordInfo<'/custom-query', '/custom-query', Record<never, never>, Record<never, never>>,
    '/flow-details-special-rate': RouteRecordInfo<'/flow-details-special-rate', '/flow-details-special-rate', Record<never, never>, Record<never, never>>,
    '/image-archive-oa': RouteRecordInfo<'/image-archive-oa', '/image-archive-oa', Record<never, never>, Record<never, never>>,
    '/menu-management': RouteRecordInfo<'/menu-management', '/menu-management', Record<never, never>, Record<never, never>>,
    '/param-config': RouteRecordInfo<'/param-config', '/param-config', Record<never, never>, Record<never, never>>,
    '/process-initiation': RouteRecordInfo<'/process-initiation', '/process-initiation', Record<never, never>, Record<never, never>>,
    '/role-management': RouteRecordInfo<'/role-management', '/role-management', Record<never, never>, Record<never, never>>,
    '/special-rate-application': RouteRecordInfo<'/special-rate-application', '/special-rate-application', Record<never, never>, Record<never, never>>,
    '/template-management': RouteRecordInfo<'/template-management', '/template-management', Record<never, never>, Record<never, never>>,
    '/template-query': RouteRecordInfo<'/template-query', '/template-query', Record<never, never>, Record<never, never>>,
    '/user-management': RouteRecordInfo<'/user-management', '/user-management', Record<never, never>, Record<never, never>>,
  }
}
