<template>
  <div class="special-rate-application">
    <h2>特殊保证金率申请</h2>

    <!-- 禁止申请时间段提示 -->
    <el-alert
      v-if="isApplicationForbidden"
      type="warning"
      :closable="false"
      show-icon
      class="forbidden-alert"
    >
      <div v-html="forbiddenMessage"></div>
    </el-alert>

    <!-- 投资者代码查询区域 -->
    <div class="investor-search">
      <el-form :inline="true" :model="basicInfo" class="justify-between-form">
        <div class="left-group">
          <el-form-item label="投资者代码">
            <el-input
              v-model="basicInfo.investorCode"
              placeholder="请输入"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleInvestorSearch"
              >查询</el-button
            >
          </el-form-item>
        </div>

        <div class="right-group">
          <el-form-item>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
            <el-button @click="handleClose">关闭</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 客户基本信息 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>客户基本信息</span>
        </div>
      </template>
      <el-form :model="basicInfo" label-width="160px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="投资者代码">
              <el-input
                v-model="basicInfo.investorCode"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="投资者名称">
              <el-input
                v-model="basicInfo.investorName"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="组织构架">
              <el-input
                v-model="basicInfo.organization"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户经理">
              <el-input
                v-model="basicInfo.manager"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="投资者性质">
              <el-input
                v-model="basicInfo.investorNature"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="投资者类型">
              <el-input
                v-model="basicInfo.investorType"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="一票否决状态">
              <el-input
                v-model="basicInfo.vetoStatus"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="最近半年强平次数">
              <el-input
                v-model="basicInfo.forcedLiquidationCount"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="近半年平均风险等级">
              <el-input
                v-model="basicInfo.avgRiskLevel"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="上月风险等级">
              <el-input
                v-model="basicInfo.lastMonthRiskLevel"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最优等级">
              <el-input
                v-model="basicInfo.bestRiskLevel"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="昨日结算后权益">
              <el-input
                v-model="basicInfo.yesterdayEquity"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="实时权益">
              <el-input
                v-model="basicInfo.realTimeEquity"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核权益">
              <el-input
                v-model="basicInfo.auditEquity"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="当前保证金标准">
              <el-input
                v-model="basicInfo.currentMarginStandard"
                disabled
                placeholder="自动匹配"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="可适配特保标准">
              <div
                v-html="basicInfo.adaptableStandard"
                class="adaptable-standard"
              ></div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 特保申请信息 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>特保申请信息</span>
        </div>
      </template>
      <el-form
        :model="applicationInfo"
        label-width="160px"
        label-position="left"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="交易习惯">
              <el-radio-group
                v-model="applicationInfo.tradingHabit"
                :disabled="isNaturalPersonAndHedging"
              >
                <el-radio value="投机/套利交易" label="投机/套利交易">投机/套利交易</el-radio>
                <el-radio value="套保交易" label="套保交易" :disabled="isNaturalPerson"
                  >套保交易</el-radio
                >
                <el-radio value="量化/对冲交易" label="量化/对冲交易">量化/对冲交易</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="申请标准">
              <div class="application-standard">
                <span>交易所基础上浮</span>
                <el-select
                  v-model="applicationInfo.floatPoint"
                  placeholder="请选择"
                  style="width: 100px; margin: 0 10px"
                >
                  <el-option label="0%" value="0" />
                  <el-option label="1%" value="1" />
                  <el-option label="2%" value="2" />
                  <el-option label="3%" value="3" />
                </el-select>
                <span>保证金率模板代码：</span>
                <span>{{ applicationInfo.templateCode }}</span>
                <span style="margin-left: 20px">保证金率模板名称：</span>
                <span>{{ applicationInfo.templateName }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="自动审核结果">
              <el-tag :type="auditResultTagType">{{
                applicationInfo.auditResult
              }}</el-tag>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 说明信息 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>说明信息</span>
        </div>
      </template>
      <div class="explanation">
        <ol class="explanation-text">
          <li>
            自动审核结果为【满足】或【基本满足】的自然人，则下一步客户可以通过长江期货APP完成信息确认。<br />
            自动审核结果为【不满足准入】的自然人，分支机构仍坚持要给客户申请特保优惠的，则客户必须通过签署纸质表单方式完成确认。<br />
            所有法人客户，无论自动审核结果如何，客户必须通过纸质表单盖章的方式完成确认。
          </li>
          <li>
            本次申请标准是否生效以期货公司OA审批结果为准，请关注OA审批记录。
          </li>
          <li>
            投资者保证金优惠标准将跟随投资者风险等级变动而动态调整，公司有权根据审核情况提高或取消投资者已申请的优惠。
          </li>
          <li>
            如申请优惠保证金率后，投资者国债期货的保证金率计算结果高于公司公称标准，则按公司公称标准收取，即投资者得到国债期货保证金率=min(申请的优惠标准,公司公称标准)，对投资者更优惠。
          </li>
        </ol>
      </div>
    </el-card>

    <!-- 确认对话框 -->
    <el-dialog v-model="dialogVisible" title="确认提交" width="30%" center>
      <span>确认提交特殊保证金率申请？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from "vue";
import { useRouter } from 'vue-router';

import { ElMessage, ElMessageBox } from "element-plus";

// 禁止申请时间段判断
const isApplicationForbidden = ref(false);
const forbiddenMessage = ref("");

// 对话框状态
const dialogVisible = ref(false);

// 客户基本信息
const basicInfo = reactive({
  investorCode: "",
  investorName: "",
  organization: "",
  manager: "",
  investorNature: "",
  investorType: "",
  vetoStatus: "",
  forcedLiquidationCount: "",
  avgRiskLevel: "",
  lastMonthRiskLevel: "",
  bestRiskLevel: "",
  yesterdayEquity: "",
  realTimeEquity: "",
  auditEquity: "",
  currentMarginStandard: "",
  adaptableStandard: "",
});

// 特保申请信息
const applicationInfo = reactive({
  tradingHabit: "投机/套利交易",
  floatPoint: "0",
  templateCode: "",
  templateName: "",
  auditResult: "",
});

// 计算属性：是否为自然人
const isNaturalPerson = computed(() => {
  return basicInfo.investorType === "自然人";
});

// 计算属性：是否为自然人且选择了套保交易
const isNaturalPersonAndHedging = computed(() => {
  return isNaturalPerson.value && applicationInfo.tradingHabit === "套保交易";
});

// 计算属性：审核结果标签类型
const auditResultTagType = computed(() => {
  if (applicationInfo.auditResult === "满足准入1") {
    return "success";
  } else if (applicationInfo.auditResult === "满足准入2") {
    return "warning";
  } else if (applicationInfo.auditResult === "不满足准入") {
    return "danger";
  }
  return "info";
});

// 监听浮动点数变化，更新模板代码和名称
watch(
  () => applicationInfo.floatPoint,
  (newValue) => {
    applicationInfo.templateCode = `6A00${newValue}A`;
    applicationInfo.templateName = `广州/上海/大连/郑州/能源/中金+${newValue}%`;
    updateAuditResult();
  }
);

// 更新审核结果
const updateAuditResult = () => {
  // 模拟审核逻辑
  const floatPoint = parseInt(applicationInfo.floatPoint);
  const riskLevel = parseInt(basicInfo.bestRiskLevel || "0");

  if (riskLevel <= 2 && floatPoint <= 1) {
    applicationInfo.auditResult = "满足准入1";
  } else if (riskLevel <= 3 && floatPoint <= 2) {
    applicationInfo.auditResult = "满足准入2";
  } else {
    applicationInfo.auditResult = "不满足准入";
  }
};

// 处理投资者查询
const handleInvestorSearch = () => {
  if (!basicInfo.investorCode) {
    ElMessage({
      message: '请输入投资者代码',
      type: 'info',
      duration: 3000,
      offset: 80
    });
    return;
  }

  // 模拟从服务器获取投资者信息
  // 实际项目中应该调用API获取数据
  setTimeout(() => {
    // 模拟数据
    basicInfo.investorName = "张三";
    basicInfo.organization = "102108-QH江汉营业部市场四部";
    basicInfo.manager = "吕攀";
    basicInfo.investorNature = "存量投资者";
    basicInfo.investorType = "自然人";
    basicInfo.vetoStatus = "否";
    basicInfo.forcedLiquidationCount = "0";
    basicInfo.avgRiskLevel = "2";
    basicInfo.lastMonthRiskLevel = "1";
    basicInfo.bestRiskLevel = "1";
    basicInfo.yesterdayEquity = "1000000";
    basicInfo.realTimeEquity = "1050000";
    basicInfo.auditEquity = "1050000";
    basicInfo.currentMarginStandard = "3A001A    上海/大连/郑州+1%";
    basicInfo.adaptableStandard =
      '<strong>交易所基础上浮0%及以上</strong>, <strong style="color: red;">满足公司标准</strong>';

    // 更新审核结果
    updateAuditResult();

    ElMessage.success("查询成功");
  }, 500);
};

// 处理提交按钮点击事件
const handleSubmit = () => {
  // 显示确认对话框
  dialogVisible.value = true;
};

// 在 setup 中获取 router 实例
const router = useRouter();

// 确认提交
const confirmSubmit = () => {
  // 关闭对话框
  dialogVisible.value = false;

  // 显示成功提示
  ElMessage.success("创建成功！请尽快完成推送APP/上传附件操作，如在T+1个交易日24点前未完成操作，该流程自动作废。");
  
  // 使用 router 跳转到流程明细页面
  router.push('/flow-details-special-rate');
};

// 处理关闭按钮点击事件
const handleClose = () => {
  // 显示提示信息

  ElMessage.info({
    message: "此页面已关闭",
    showClose: true,
  });
  // 实际项目中这里应该返回上一页或关闭当前页面
  // 可以使用路由导航返回上一页
  // router.push('/some-path')
};

// 检查是否在禁止申请时间段
const checkForbiddenPeriod = () => {
  // 模拟从服务器获取禁止申请时间段
  // 实际项目中应该调用API获取数据
  setTimeout(() => {
    // 为了演示效果，这里设置为true
    isApplicationForbidden.value = true;
    forbiddenMessage.value =
      "2025劳动节前暂停特保申请，暂停时间为:2025-04-23 16:00:00到2025-05-06 08:30:00。暂停受理客户类型为【所有客户】";
  }, 500);
};

// 页面加载时检查是否在禁止申请时间段
onMounted(() => {
  checkForbiddenPeriod();
});
</script>

<style lang="scss" scoped>
.special-rate-application {
  padding: 20px;
}

.forbidden-alert {
  margin-bottom: 20px;
}

.investor-search {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.investor-search .el-form {
  width: 100%;
}

.justify-between-form {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 100%;
}

.left-group {
  display: flex;
  align-items: center;
}

.right-group {
  display: flex;
  align-items: center;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.application-standard {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.adaptable-standard :deep(strong) {
  font-weight: bold;
}

.explanation {
  line-height: 1.6;
  text-align: left;
}

.explanation ol {
  padding-left: 20px;
  margin: 0;
}

.explanation li {
  margin-bottom: 10px;
}

.explanation-text {
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  width: 100%;
}

</style>




